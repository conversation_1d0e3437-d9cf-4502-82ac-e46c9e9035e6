package xiaozhi.modules.meeting.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import xiaozhi.common.utils.DateUtils;

/**
 * 会议详情VO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "会议详情VO")
public class MeetingDetailVO implements Serializable {

    @Schema(description = "会议唯一标识")
    private String id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "会议标题")
    private String title;

    @Schema(description = "会议状态")
    private String status;

    @Schema(description = "会议状态描述")
    private String statusDesc;

    @Schema(description = "会议开始时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date startTime;

    @Schema(description = "会议结束时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date endTime;

    @Schema(description = "会议总时长（秒）")
    private Integer totalDuration;

    @Schema(description = "会议总时长（格式化）")
    private String totalDurationFormatted;

    @Schema(description = "会议片段总数")
    private Integer totalSegments;

    @Schema(description = "会议总字数")
    private Integer totalWords;

    @Schema(description = "会议片段列表")
    private List<MeetingSegmentVO> segments;

    @Schema(description = "会议分析结果列表")
    private List<MeetingAnalysisVO> analysisResults;

    @Schema(description = "会议问答记录列表")
    private List<MeetingQaVO> qaRecords;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createDate;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date updateDate;

    /**
     * 格式化时长显示
     */
    public String getTotalDurationFormatted() {
        if (totalDuration == null || totalDuration <= 0) {
            return "0分钟";
        }
        
        int hours = totalDuration / 3600;
        int minutes = (totalDuration % 3600) / 60;
        int seconds = totalDuration % 60;
        
        StringBuilder sb = new StringBuilder();
        if (hours > 0) {
            sb.append(hours).append("小时");
        }
        if (minutes > 0) {
            sb.append(minutes).append("分钟");
        }
        if (seconds > 0 && hours == 0) {
            sb.append(seconds).append("秒");
        }
        
        return sb.length() > 0 ? sb.toString() : "0分钟";
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if ("ACTIVE".equals(status)) {
            return "进行中";
        } else if ("COMPLETED".equals(status)) {
            return "已完成";
        } else if ("CANCELLED".equals(status)) {
            return "已取消";
        }
        return status;
    }
}
