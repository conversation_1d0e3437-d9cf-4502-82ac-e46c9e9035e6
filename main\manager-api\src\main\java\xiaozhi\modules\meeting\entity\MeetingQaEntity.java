package xiaozhi.modules.meeting.entity;

import java.math.BigDecimal;
import java.util.Date;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会议问答记录实体类
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "ai_meeting_qa", autoResultMap = true)
@Schema(description = "会议问答记录")
public class MeetingQaEntity {

    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description = "问答记录唯一标识")
    private String id;

    @Schema(description = "会议ID（关联会议表）")
    private String meetingId;

    @Schema(description = "用户问题")
    private String question;

    @Schema(description = "智能回答")
    private String answer;

    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "相关片段上下文（JSON格式）")
    private JSONObject contextSegments;

    @Schema(description = "答案置信度（0-1）")
    private BigDecimal confidenceScore;

    @Schema(description = "响应时间（毫秒）")
    private Integer responseTime;

    @Schema(description = "创建者ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
}
