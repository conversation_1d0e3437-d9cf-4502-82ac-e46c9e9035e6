package xiaozhi.modules.meeting.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import xiaozhi.common.utils.DateUtils;

/**
 * 会议问答记录VO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "会议问答记录VO")
public class MeetingQaVO implements Serializable {

    @Schema(description = "问答记录唯一标识")
    private String id;

    @Schema(description = "会议ID")
    private String meetingId;

    @Schema(description = "用户问题")
    private String question;

    @Schema(description = "智能回答")
    private String answer;

    @Schema(description = "相关片段上下文")
    private JSONObject contextSegments;

    @Schema(description = "答案置信度（0-1）")
    private BigDecimal confidenceScore;

    @Schema(description = "置信度百分比")
    private String confidencePercentage;

    @Schema(description = "响应时间（毫秒）")
    private Integer responseTime;

    @Schema(description = "响应时间（格式化）")
    private String responseTimeFormatted;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createDate;

    /**
     * 获取置信度百分比
     */
    public String getConfidencePercentage() {
        if (confidenceScore == null) {
            return "0%";
        }
        return String.format("%.1f%%", confidenceScore.multiply(new BigDecimal("100")).doubleValue());
    }

    /**
     * 格式化响应时间
     */
    public String getResponseTimeFormatted() {
        if (responseTime == null || responseTime <= 0) {
            return "0ms";
        }
        
        if (responseTime < 1000) {
            return responseTime + "ms";
        } else {
            double seconds = responseTime / 1000.0;
            return String.format("%.1fs", seconds);
        }
    }
}
