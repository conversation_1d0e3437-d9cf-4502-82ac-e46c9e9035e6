package xiaozhi.modules.meeting.entity;

import java.util.Date;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会议片段实体类
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "ai_meeting_segment", autoResultMap = true)
@Schema(description = "会议片段")
public class MeetingSegmentEntity {

    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description = "片段唯一标识")
    private String id;

    @Schema(description = "会议ID（关联会议表）")
    private String meetingId;

    @Schema(description = "片段序号")
    private Integer segmentIndex;

    @Schema(description = "片段内容")
    private String content;

    @Schema(description = "片段字数")
    private Integer wordCount;

    @Schema(description = "片段开始时间")
    private Date startTime;

    @Schema(description = "片段结束时间")
    private Date endTime;

    @Schema(description = "片段时长（秒）")
    private Integer duration;

    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "说话人信息（JSON格式）")
    private JSONObject speakerInfo;

    @Schema(description = "创建者ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    @Schema(description = "更新者ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}
