package xiaozhi.modules.meeting.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.meeting.entity.MeetingEntity;

/**
 * 会议记录DAO接口
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Mapper
public interface MeetingDao extends BaseMapper<MeetingEntity> {

    /**
     * 根据用户ID查询会议列表
     * 
     * @param userId 用户ID
     * @return 会议列表
     */
    @Select("SELECT * FROM ai_meeting WHERE user_id = #{userId} ORDER BY start_time DESC")
    List<MeetingEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据设备ID查询会议列表
     * 
     * @param deviceId 设备ID
     * @return 会议列表
     */
    @Select("SELECT * FROM ai_meeting WHERE device_id = #{deviceId} ORDER BY start_time DESC")
    List<MeetingEntity> selectByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 根据状态查询会议列表
     * 
     * @param status 会议状态
     * @return 会议列表
     */
    @Select("SELECT * FROM ai_meeting WHERE status = #{status} ORDER BY start_time DESC")
    List<MeetingEntity> selectByStatus(@Param("status") String status);

    /**
     * 根据用户ID和状态查询会议列表
     * 
     * @param userId 用户ID
     * @param status 会议状态
     * @return 会议列表
     */
    @Select("SELECT * FROM ai_meeting WHERE user_id = #{userId} AND status = #{status} ORDER BY start_time DESC")
    List<MeetingEntity> selectByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据时间范围查询会议列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会议列表
     */
    @Select("SELECT * FROM ai_meeting WHERE user_id = #{userId} AND start_time >= #{startTime} AND start_time <= #{endTime} ORDER BY start_time DESC")
    List<MeetingEntity> selectByTimeRange(@Param("userId") Long userId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 更新会议状态
     * 
     * @param id 会议ID
     * @param status 新状态
     * @param endTime 结束时间
     * @param totalDuration 总时长
     * @return 更新行数
     */
    @Update("UPDATE ai_meeting SET status = #{status}, end_time = #{endTime}, total_duration = #{totalDuration}, update_date = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("status") String status, @Param("endTime") Date endTime, @Param("totalDuration") Integer totalDuration);

    /**
     * 更新会议统计信息
     * 
     * @param id 会议ID
     * @param totalSegments 总片段数
     * @param totalWords 总字数
     * @return 更新行数
     */
    @Update("UPDATE ai_meeting SET total_segments = #{totalSegments}, total_words = #{totalWords}, update_date = NOW() WHERE id = #{id}")
    int updateStatistics(@Param("id") String id, @Param("totalSegments") Integer totalSegments, @Param("totalWords") Integer totalWords);

    /**
     * 根据关键词搜索会议
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 会议列表
     */
    @Select("SELECT * FROM ai_meeting WHERE user_id = #{userId} AND title LIKE CONCAT('%', #{keyword}, '%') ORDER BY start_time DESC")
    List<MeetingEntity> searchByKeyword(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 统计用户会议数量
     * 
     * @param userId 用户ID
     * @return 会议数量
     */
    @Select("SELECT COUNT(*) FROM ai_meeting WHERE user_id = #{userId}")
    Integer countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户指定状态的会议数量
     * 
     * @param userId 用户ID
     * @param status 会议状态
     * @return 会议数量
     */
    @Select("SELECT COUNT(*) FROM ai_meeting WHERE user_id = #{userId} AND status = #{status}")
    Integer countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);
}
