package xiaozhi.modules.meeting.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会议记录实体类
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_meeting")
@Schema(description = "会议记录")
public class MeetingEntity {

    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description = "会议唯一标识")
    private String id;

    @Schema(description = "用户ID（关联用户表）")
    private Long userId;

    @Schema(description = "设备ID（关联设备表）")
    private String deviceId;

    @Schema(description = "会议标题")
    private String title;

    @Schema(description = "会议状态：ACTIVE-进行中，COMPLETED-已完成，CANCELLED-已取消")
    private String status;

    @Schema(description = "会议开始时间")
    private Date startTime;

    @Schema(description = "会议结束时间")
    private Date endTime;

    @Schema(description = "会议总时长（秒）")
    private Integer totalDuration;

    @Schema(description = "会议片段总数")
    private Integer totalSegments;

    @Schema(description = "会议总字数")
    private Integer totalWords;

    @Schema(description = "创建者ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    @Schema(description = "更新者ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;

    /**
     * 会议状态枚举
     */
    public enum Status {
        ACTIVE("ACTIVE", "进行中"),
        COMPLETED("COMPLETED", "已完成"),
        CANCELLED("CANCELLED", "已取消");

        private final String code;
        private final String desc;

        Status(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
