package xiaozhi.modules.meeting.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import xiaozhi.common.utils.DateUtils;

/**
 * 会议分析结果VO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "会议分析结果VO")
public class MeetingAnalysisVO implements Serializable {

    @Schema(description = "分析结果唯一标识")
    private String id;

    @Schema(description = "会议ID")
    private String meetingId;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "分析类型描述")
    private String analysisTypeDesc;

    @Schema(description = "分析结果标题")
    private String title;

    @Schema(description = "分析结果内容")
    private String content;

    @Schema(description = "分析结果字数")
    private Integer wordCount;

    @Schema(description = "分析时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date analysisTime;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createDate;

    /**
     * 获取分析类型描述
     */
    public String getAnalysisTypeDesc() {
        if ("minutes".equals(analysisType)) {
            return "会议纪要";
        } else if ("summary".equals(analysisType)) {
            return "会议总结";
        } else if ("suggestions".equals(analysisType)) {
            return "建议方案";
        }
        return analysisType;
    }
}
