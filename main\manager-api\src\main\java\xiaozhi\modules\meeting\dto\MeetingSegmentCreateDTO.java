package xiaozhi.modules.meeting.dto;

import java.io.Serializable;
import java.util.Date;


import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 创建会议片段DTO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "创建会议片段DTO")
public class MeetingSegmentCreateDTO implements Serializable {

    @Schema(description = "会议ID", required = true)
    @NotBlank(message = "会议ID不能为空")
    private String meetingId;

    @Schema(description = "片段序号", required = true)
    @NotNull(message = "片段序号不能为空")
    private Integer segmentIndex;

    @Schema(description = "片段内容")
    private String content;

    @Schema(description = "片段字数")
    private Integer wordCount;

    @Schema(description = "片段开始时间", required = true)
    @NotNull(message = "片段开始时间不能为空")
    private Date startTime;

    @Schema(description = "说话人信息（JSON格式）")
    private JSONObject speakerInfo;
}
