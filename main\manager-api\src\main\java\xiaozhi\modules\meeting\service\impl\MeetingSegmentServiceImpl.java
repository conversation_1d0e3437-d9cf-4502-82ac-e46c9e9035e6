package xiaozhi.modules.meeting.service.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.meeting.dao.MeetingSegmentDao;
import xiaozhi.modules.meeting.dto.MeetingSegmentCreateDTO;
import xiaozhi.modules.meeting.dto.MeetingSegmentUpdateDTO;
import xiaozhi.modules.meeting.entity.MeetingSegmentEntity;
import xiaozhi.modules.meeting.service.MeetingSegmentService;
import xiaozhi.modules.meeting.vo.MeetingSegmentVO;

/**
 * 会议片段服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@Service
public class MeetingSegmentServiceImpl extends BaseServiceImpl<MeetingSegmentDao, MeetingSegmentEntity> implements MeetingSegmentService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MeetingSegmentVO create(MeetingSegmentCreateDTO dto) {
        MeetingSegmentEntity entity = ConvertUtils.sourceToTarget(dto, MeetingSegmentEntity.class);
        
        // 保存会议片段
        insert(entity);
        
        log.info("创建会议片段成功，片段ID: {}, 会议ID: {}, 序号: {}", 
                entity.getId(), entity.getMeetingId(), entity.getSegmentIndex());
        
        return ConvertUtils.sourceToTarget(entity, MeetingSegmentVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MeetingSegmentUpdateDTO dto) {
        MeetingSegmentEntity entity = baseDao.selectById(dto.getId());
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        // 更新字段
        if (dto.getContent() != null) {
            entity.setContent(dto.getContent());
        }
        if (dto.getWordCount() != null) {
            entity.setWordCount(dto.getWordCount());
        }
        if (dto.getEndTime() != null) {
            entity.setEndTime(dto.getEndTime());
        }
        if (dto.getDuration() != null) {
            entity.setDuration(dto.getDuration());
        }
        if (dto.getSpeakerInfo() != null) {
            entity.setSpeakerInfo(dto.getSpeakerInfo());
        }
        
        updateById(entity);
        
        log.info("更新会议片段成功，片段ID: {}", dto.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        MeetingSegmentEntity entity = baseDao.selectById(id);
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        baseDao.deleteById(id);
        
        log.info("删除会议片段成功，片段ID: {}", id);
    }

    @Override
    public List<MeetingSegmentVO> getByMeetingId(String meetingId) {
        List<MeetingSegmentEntity> entities = baseDao.selectByMeetingId(meetingId);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingSegmentVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public MeetingSegmentVO getByMeetingIdAndIndex(String meetingId, Integer segmentIndex) {
        MeetingSegmentEntity entity = baseDao.selectByMeetingIdAndIndex(meetingId, segmentIndex);
        if (entity == null) {
            return null;
        }
        return ConvertUtils.sourceToTarget(entity, MeetingSegmentVO.class);
    }

    @Override
    public MeetingSegmentVO getLatestByMeetingId(String meetingId) {
        MeetingSegmentEntity entity = baseDao.selectLatestByMeetingId(meetingId);
        if (entity == null) {
            return null;
        }
        return ConvertUtils.sourceToTarget(entity, MeetingSegmentVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateContent(String id, String content, Integer wordCount) {
        int result = baseDao.updateContent(id, content, wordCount);
        if (result == 0) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        log.debug("更新片段内容成功，片段ID: {}, 字数: {}", id, wordCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEndTimeAndDuration(String id, Date endTime, Integer duration) {
        int result = baseDao.updateEndTimeAndDuration(id, endTime, duration);
        if (result == 0) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        log.debug("更新片段时间信息成功，片段ID: {}, 时长: {}秒", id, duration);
    }

    @Override
    public List<MeetingSegmentVO> searchByKeyword(String meetingId, String keyword) {
        List<MeetingSegmentEntity> entities = baseDao.searchByKeyword(meetingId, keyword);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingSegmentVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingSegmentVO> getByTimeRange(String meetingId, Date startTime, Date endTime) {
        List<MeetingSegmentEntity> entities = baseDao.selectByTimeRange(meetingId, startTime, endTime);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingSegmentVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public String getFullContentByMeetingId(String meetingId) {
        return baseDao.selectFullContentByMeetingId(meetingId);
    }

    @Override
    public Integer countByMeetingId(String meetingId) {
        return baseDao.countByMeetingId(meetingId);
    }

    @Override
    public Integer sumWordCountByMeetingId(String meetingId) {
        return baseDao.sumWordCountByMeetingId(meetingId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMeetingId(String meetingId) {
        int result = baseDao.deleteByMeetingId(meetingId);
        
        log.info("删除会议所有片段成功，会议ID: {}, 删除数量: {}", meetingId, result);
    }

    @Override
    public MeetingSegmentVO getById(String id) {
        MeetingSegmentEntity entity = baseDao.selectById(id);
        if (entity == null) {
            return null;
        }
        return ConvertUtils.sourceToTarget(entity, MeetingSegmentVO.class);
    }
}
