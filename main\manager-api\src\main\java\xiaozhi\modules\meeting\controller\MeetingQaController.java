package xiaozhi.modules.meeting.controller;

import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.annotation.LogOperation;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.Result;
import xiaozhi.common.utils.ResultUtils;
import xiaozhi.modules.meeting.dto.MeetingQaCreateDTO;
import xiaozhi.modules.meeting.service.MeetingQaService;
import xiaozhi.modules.meeting.vo.MeetingQaVO;
import xiaozhi.modules.security.user.SecurityUser;

/**
 * 会议问答管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/meeting/qa")
@Tag(name = "会议问答管理")
public class MeetingQaController {

    private final MeetingQaService meetingQaService;

    @GetMapping("/meeting/{meetingId}")
    @Operation(summary = "获取会议的所有问答记录")
    @RequiresPermissions("sys:role:normal")
    public Result<List<MeetingQaVO>> getByMeetingId(@PathVariable("meetingId") String meetingId) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        List<MeetingQaVO> qaList = meetingQaService.getByMeetingId(meetingId);
        
        log.info("用户{}获取会议问答记录列表，会议ID: {}, 问答数量: {}", user.getId(), meetingId, qaList.size());
        
        return ResultUtils.success(qaList);
    }


    @PostMapping
    @Operation(summary = "创建问答记录")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("创建问答记录")
    public Result<MeetingQaVO> create(@RequestBody @Valid MeetingQaCreateDTO dto) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        MeetingQaVO qa = meetingQaService.create(dto);
        
        log.info("用户{}创建会议问答记录成功，问答ID: {}, 会议ID: {}", 
                user.getId(), qa.getId(), qa.getMeetingId());
        
        return ResultUtils.success(qa);
    }



    @DeleteMapping("/{id}")
    @Operation(summary = "删除问答记录")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("删除问答记录")
    public Result<Void> delete(@PathVariable("id") String id) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        meetingQaService.delete(id);
        
        log.info("用户{}删除会议问答记录，问答ID: {}", user.getId(), id);
        
        return ResultUtils.success(null);
    }

}
