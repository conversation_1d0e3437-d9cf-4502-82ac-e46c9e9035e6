package xiaozhi.modules.meeting.service;

import java.util.List;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.meeting.dto.MeetingQaCreateDTO;
import xiaozhi.modules.meeting.entity.MeetingQaEntity;
import xiaozhi.modules.meeting.vo.MeetingQaVO;

/**
 * 会议问答记录服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface MeetingQaService extends BaseService<MeetingQaEntity> {

    /**
     * 创建会议问答记录
     * 
     * @param dto 创建会议问答记录参数
     * @return 会议问答记录信息
     */
    MeetingQaVO create(MeetingQaCreateDTO dto);

    /**
     * 删除会议问答记录
     * 
     * @param id 问答记录ID
     */
    void delete(String id);

    /**
     * 根据会议ID获取所有问答记录
     * 
     * @param meetingId 会议ID
     * @return 问答记录列表
     */
    List<MeetingQaVO> getByMeetingId(String meetingId);

    /**
     * 根据用户ID获取问答记录
     * 
     * @param userId 用户ID
     * @return 问答记录列表
     */
    List<MeetingQaVO> getByUserId(Long userId);

    /**
     * 根据问题关键词搜索问答记录
     * 
     * @param meetingId 会议ID
     * @param keyword 关键词
     * @return 问答记录列表
     */
    List<MeetingQaVO> searchByQuestion(String meetingId, String keyword);

    /**
     * 根据答案关键词搜索问答记录
     * 
     * @param meetingId 会议ID
     * @param keyword 关键词
     * @return 问答记录列表
     */
    List<MeetingQaVO> searchByAnswer(String meetingId, String keyword);

    /**
     * 根据关键词搜索问答记录（问题和答案）
     * 
     * @param meetingId 会议ID
     * @param keyword 关键词
     * @return 问答记录列表
     */
    List<MeetingQaVO> searchByKeyword(String meetingId, String keyword);

    /**
     * 根据用户ID和关键词搜索问答记录
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 问答记录列表
     */
    List<MeetingQaVO> searchByUserIdAndKeyword(Long userId, String keyword);

    /**
     * 统计会议的问答记录数量
     * 
     * @param meetingId 会议ID
     * @return 问答记录数量
     */
    Integer countByMeetingId(String meetingId);

    /**
     * 统计用户的问答记录数量
     * 
     * @param userId 用户ID
     * @return 问答记录数量
     */
    Integer countByUserId(Long userId);

    /**
     * 获取平均响应时间
     * 
     * @param meetingId 会议ID
     * @return 平均响应时间（毫秒）
     */
    Double getAverageResponseTime(String meetingId);

    /**
     * 获取平均置信度
     * 
     * @param meetingId 会议ID
     * @return 平均置信度
     */
    Double getAverageConfidenceScore(String meetingId);

    /**
     * 删除会议的所有问答记录
     * 
     * @param meetingId 会议ID
     */
    void deleteByMeetingId(String meetingId);
}
