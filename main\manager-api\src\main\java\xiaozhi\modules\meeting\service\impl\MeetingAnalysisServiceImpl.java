package xiaozhi.modules.meeting.service.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.meeting.dao.MeetingAnalysisDao;
import xiaozhi.modules.meeting.dto.MeetingAnalysisCreateDTO;
import xiaozhi.modules.meeting.entity.MeetingAnalysisEntity;
import xiaozhi.modules.meeting.service.MeetingAnalysisService;
import xiaozhi.modules.meeting.vo.MeetingAnalysisVO;

/**
 * 会议分析结果服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@Service
public class MeetingAnalysisServiceImpl extends BaseServiceImpl<MeetingAnalysisDao, MeetingAnalysisEntity> implements MeetingAnalysisService {

    

    @Override
    public MeetingAnalysisVO getById(String id) {
        MeetingAnalysisEntity entity = baseDao.selectById(id);
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        return ConvertUtils.sourceToTarget(entity, MeetingAnalysisVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MeetingAnalysisVO create(MeetingAnalysisCreateDTO dto) {
        MeetingAnalysisEntity entity = ConvertUtils.sourceToTarget(dto, MeetingAnalysisEntity.class);
        
        // 设置分析时间
        entity.setAnalysisTime(new Date());
        
        // 保存分析结果
        insert(entity);
        
        log.info("创建会议分析结果成功，分析ID: {}, 会议ID: {}, 类型: {}", 
                entity.getId(), entity.getMeetingId(), entity.getAnalysisType());
        
        return ConvertUtils.sourceToTarget(entity, MeetingAnalysisVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        MeetingAnalysisEntity entity = baseDao.selectById(id);
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        baseDao.deleteById(id);
        
        log.info("删除会议分析结果成功，分析ID: {}", id);
    }

    @Override
    public List<MeetingAnalysisVO> getByMeetingId(String meetingId) {
        List<MeetingAnalysisEntity> entities = baseDao.selectByMeetingId(meetingId);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingAnalysisVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public MeetingAnalysisVO getByMeetingIdAndType(String meetingId, String analysisType) {
        MeetingAnalysisEntity entity = baseDao.selectByMeetingIdAndType(meetingId, analysisType);
        if (entity == null) {
            return null;
        }
        return ConvertUtils.sourceToTarget(entity, MeetingAnalysisVO.class);
    }

    @Override
    public List<MeetingAnalysisVO> getByAnalysisType(String analysisType) {
        List<MeetingAnalysisEntity> entities = baseDao.selectByAnalysisType(analysisType);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingAnalysisVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingAnalysisVO> getByUserId(Long userId) {
        List<MeetingAnalysisEntity> entities = baseDao.selectByUserId(userId);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingAnalysisVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingAnalysisVO> getByUserIdAndType(Long userId, String analysisType) {
        List<MeetingAnalysisEntity> entities = baseDao.selectByUserIdAndType(userId, analysisType);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingAnalysisVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingAnalysisVO> searchByKeyword(String keyword) {
        List<MeetingAnalysisEntity> entities = baseDao.searchByKeyword(keyword);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingAnalysisVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingAnalysisVO> searchByUserIdAndKeyword(Long userId, String keyword) {
        List<MeetingAnalysisEntity> entities = baseDao.searchByUserIdAndKeyword(userId, keyword);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingAnalysisVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public Integer countByMeetingId(String meetingId) {
        return baseDao.countByMeetingId(meetingId);
    }

    @Override
    public Integer countByMeetingIdAndType(String meetingId, String analysisType) {
        return baseDao.countByMeetingIdAndType(meetingId, analysisType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMeetingId(String meetingId) {
        int result = baseDao.deleteByMeetingId(meetingId);
        
        log.info("删除会议所有分析结果成功，会议ID: {}, 删除数量: {}", meetingId, result);
    }
}
