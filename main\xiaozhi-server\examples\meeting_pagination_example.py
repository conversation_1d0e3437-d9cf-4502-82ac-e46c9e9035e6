#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会议分页查询示例
演示如何使用新的分页API获取会议列表
"""

import asyncio
import json
from datetime import datetime, timedelta
from core.meeting_manager import MeetingApiClient
from core.utils.logger import get_logger

logger = get_logger()

async def demo_meeting_pagination():
    """演示会议分页查询功能"""
    
    # 模拟配置
    config = {
        "manager-api": {
            "base_url": "http://localhost:8080",
            "auth_token": "your_auth_token_here"
        }
    }
    
    # 创建API客户端
    api_client = MeetingApiClient(config)
    
    print("=== 会议分页查询示例 ===\n")
    
    # 1. 获取最近会议列表（第一页，每页5条）
    print("1. 获取最近会议列表（第一页，每页5条）")
    recent_meetings = await api_client.get_recent_meetings(page=1, limit=5)
    print(f"返回结果: {json.dumps(recent_meetings, indent=2, ensure_ascii=False)}")
    print(f"总数: {recent_meetings.get('total', 0)}, 当前页数据: {len(recent_meetings.get('list', []))}\n")
    
    # 2. 获取指定设备的会议列表
    print("2. 获取指定设备的会议列表")
    device_meetings = await api_client.get_recent_meetings(
        page=1, 
        limit=10, 
        device_id="ESP32_001"
    )
    print(f"设备ESP32_001的会议数量: {len(device_meetings.get('list', []))}\n")
    
    # 3. 获取指定状态的会议列表
    print("3. 获取已完成的会议列表")
    completed_meetings = await api_client.get_recent_meetings(
        page=1, 
        limit=10, 
        status="COMPLETED"
    )
    print(f"已完成会议数量: {len(completed_meetings.get('list', []))}\n")
    
    # 4. 使用完整的分页查询（带关键词搜索）
    print("4. 使用关键词搜索会议")
    search_meetings = await api_client.get_meeting_page(
        page=1,
        limit=10,
        keyword="项目讨论",
        status="COMPLETED"
    )
    print(f"搜索'项目讨论'的会议数量: {len(search_meetings.get('list', []))}\n")
    
    # 5. 按时间范围查询会议
    print("5. 查询最近7天的会议")
    end_time = datetime.now()
    start_time = end_time - timedelta(days=7)
    
    time_range_meetings = await api_client.get_meeting_page(
        page=1,
        limit=20,
        start_time=start_time.isoformat(),
        end_time=end_time.isoformat()
    )
    print(f"最近7天会议数量: {len(time_range_meetings.get('list', []))}\n")
    
    # 6. 分页遍历所有会议
    print("6. 分页遍历所有会议")
    page = 1
    limit = 5
    total_meetings = 0
    
    while True:
        page_data = await api_client.get_meeting_page(page=page, limit=limit)
        meetings = page_data.get('list', [])
        total = page_data.get('total', 0)
        
        if not meetings:
            break
            
        print(f"第{page}页: {len(meetings)}条会议")
        total_meetings += len(meetings)
        
        # 显示每个会议的基本信息
        for meeting in meetings:
            print(f"  - {meeting.get('title', 'N/A')} ({meeting.get('status', 'N/A')}) - {meeting.get('startTime', 'N/A')}")
        
        # 如果已经是最后一页，退出
        if page * limit >= total:
            break
            
        page += 1
        
        # 避免请求过快
        await asyncio.sleep(0.5)
    
    print(f"\n总共遍历了 {total_meetings} 个会议")

async def demo_meeting_statistics():
    """演示会议统计功能"""
    
    config = {
        "manager-api": {
            "base_url": "http://localhost:8080",
            "auth_token": "your_auth_token_here"
        }
    }
    
    api_client = MeetingApiClient(config)
    
    print("\n=== 会议统计示例 ===\n")
    
    # 统计不同状态的会议数量
    statuses = ["ACTIVE", "COMPLETED", "CANCELLED"]
    
    for status in statuses:
        meetings = await api_client.get_recent_meetings(page=1, limit=1000, status=status)
        count = meetings.get('total', 0)
        print(f"{status} 状态的会议数量: {count}")
    
    # 统计总会议数量
    all_meetings = await api_client.get_meeting_page(page=1, limit=1)
    total_count = all_meetings.get('total', 0)
    print(f"总会议数量: {total_count}")

if __name__ == "__main__":
    print("会议分页查询功能演示")
    print("注意: 请确保manager-api服务正在运行，并配置正确的认证信息")
    
    # 运行演示
    asyncio.run(demo_meeting_pagination())
    asyncio.run(demo_meeting_statistics())
