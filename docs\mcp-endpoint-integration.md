# MCP 接入点使用指南

本教程以虾哥开源的mcp计算器功能为示例，介绍如何将自己自定义的mcp服务接入到自己的接入点里。

本教程的前提是，你的`xiaozhi-server`已经启用了mcp接入点功能，如果你还没启用，可以先根据[这个教程](./mcp-endpoint-enable.md)启用。

# 如何为智能体接入一个简单的mcp功能，如计算器功能

### 如果你是全模块部署
如果你是全模块部署，你可以进入智控台，智能体管理，点击`配置角色`，在`意图识别`的右边，有一个`编辑功能`的按钮。

点击这个按钮。在弹出的页面里，位于底部，会有`MCP接入点`，正常来说，会显示这个智能体的`MCP接入点地址`，接下来，我们来给这个智能体扩展一个基于MCP技术的计算器的功能。

这个`MCP接入点地址`很重要，你等一下会用到。

### 如果你是单模块部署
如果你是单模块部署，且你已经在配置文件里配置了MCP接入点地址，那么正常来说，单模块部署启动的时候，会输出如下的日志。
```
250705[__main__]-INFO-初始化组件: vad成功 SileroVAD
250705[__main__]-INFO-初始化组件: asr成功 FunASRServer
250705[__main__]-INFO-OTA接口是          http://************:8002/xiaozhi/ota/
250705[__main__]-INFO-视觉分析接口是     http://************:8002/mcp/vision/explain
250705[__main__]-INFO-mcp接入点是        ws://************:8004/mcp_endpoint/mcp/?token=abc
250705[__main__]-INFO-Websocket地址是    ws://************:8000/xiaozhi/v1/
250705[__main__]-INFO-=======上面的地址是websocket协议地址，请勿用浏览器访问=======
250705[__main__]-INFO-如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
250705[__main__]-INFO-=============================================================
```

如上，输出`mcp接入点是`中`ws://************:8004/mcp_endpoint/mcp/?token=abc`就是你的`MCP接入点地址`。

这个`MCP接入点地址`很重要，你等一下会用到。

## 第一步 下载虾哥MCP计算器项目代码

浏览器打开虾哥写的[计算器项目](https://github.com/78/mcp-calculator)，

打开完，找到页面中一个绿色的按钮，写着`Code`的按钮，点开它，然后你就看到`Download ZIP`的按钮。

点击它，下载本项目源码压缩包。下载到你电脑后，解压它，此时它的名字可能叫`mcp-calculatorr-main`
你需要把它重命名成`mcp-calculator`。接下来，我们用命令行进入项目目录即安装依赖


```bash
# 进入项目目录
cd mcp-calculator

conda remove -n mcp-calculator --all -y
conda create -n mcp-calculator python=3.10 -y
conda activate mcp-calculator

pip install -r requirements.txt
```

## 第二步 启动

启动前，先从你的智控台的智能体里，复制到了MCP接入点的地址。

例如我的智能体的mcp地址是
```
ws://************:8004/mcp_endpoint/mcp/?token=abc
```

开始输入命令

```bash
export MCP_ENDPOINT=ws://************:8004/mcp_endpoint/mcp/?token=abc
```

输入完后，启动程序

```bash
python mcp_pipe.py calculator.py
```

### 如果你是智控台部署
如果你是智控台部署，启动完后，你再进入智控台，点击刷新MCP的接入状态，就会看到你扩展的功能列表了。

### 如果你是单模块部署
如果你是单模块部署，当设备连接后，会输出类似的日志，说明成功了

```
250705 -INFO-正在初始化MCP接入点: wss://2662r3426b.vicp.fun/mcp_e 
250705 -INFO-发送MCP接入点初始化消息
250705 -INFO-MCP接入点连接成功
250705 -INFO-MCP接入点初始化成功
250705 -INFO-统一工具处理器初始化完成
250705 -INFO-MCP接入点服务器信息: name=Calculator, version=1.9.4
250705 -INFO-MCP接入点支持的工具数量: 1
250705 -INFO-所有MCP接入点工具已获取，客户端准备就绪
250705 -INFO-工具缓存已刷新
250705 -INFO-当前支持的函数列表: [ 'get_time', 'get_lunar', 'play_music', 'get_weather', 'handle_exit_intent', 'calculator']
```
如果包含了 `'calculator'`，说明设备将可以根据意图识别，调用计算器这个工具。