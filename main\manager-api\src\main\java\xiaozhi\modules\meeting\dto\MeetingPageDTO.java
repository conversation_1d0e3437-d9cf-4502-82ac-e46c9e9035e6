package xiaozhi.modules.meeting.dto;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 会议分页查询DTO
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "会议分页查询DTO")
public class MeetingPageDTO implements Serializable {

    @Schema(description = "页数")
    @Min(value = 0, message = "{page.number}")
    private String page;

    @Schema(description = "显示列数")
    @Min(value = 0, message = "{limit.number}")
    private String limit;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "会议状态：ACTIVE-进行中，COMPLETED-已完成，CANCELLED-已取消")
    private String status;

    @Schema(description = "关键词搜索（标题）")
    private String keyword;

    @Schema(description = "开始时间（查询范围）")
    private Date startTime;

    @Schema(description = "结束时间（查询范围）")
    private Date endTime;
}
