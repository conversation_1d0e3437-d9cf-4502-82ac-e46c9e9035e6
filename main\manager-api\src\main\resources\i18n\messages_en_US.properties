#English
500=Server internal exception
401=Unauthorized
403=Access denied, no permissions
10001={0} cannot be empty
10002=The record already exists in the database
10003=Failed to get parameters
10004=The account number or password is incorrect.
10005=Account has been deactivated
10006=Unique ID cannot be empty
10007=The verification code is incorrect
10008=First delete submenu or button
10009=The original password is incorrect

10011=The superior department made a wrong choice
10012=Upper menu cannot be for itself
10013=Data permission interface, which can only be a Map type parameter.
10014=Please delete the subordinate department first
10015=Please delete the user under the department first

10019=Please upload a file
10020=token cannot be empty
10021=token is invalid, please log in again
10022=The account has been locked

10024=Failed to upload file {0}

10027=Redis service exception
10028=Timed task failed
10029=Cannot contain illegal characters

10030=The password is less than {0} digits.
10031=The password must consist of numbers, uppercase and lowercase letters, and special characters at the same time
10032=Exception in deleting this data
10033=Device verification code error

10034=Parameter value cannot be empty
10035=Parameter type cannot be empty
10036=Unsupported parameter type
10037=Parameter value must be a valid number
10038=Parameter value must be true or false
10039=Parameter value must be a valid JSON array format
10040=Parameter value must be a valid JSON format

10041=Device not found
10042={0}