package xiaozhi.modules.meeting.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.meeting.entity.MeetingSegmentEntity;

/**
 * 会议片段DAO接口
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Mapper
public interface MeetingSegmentDao extends BaseMapper<MeetingSegmentEntity> {

    /**
     * 根据会议ID查询所有片段
     * 
     * @param meetingId 会议ID
     * @return 片段列表
     */
    @Select("SELECT * FROM ai_meeting_segment WHERE meeting_id = #{meetingId} ORDER BY segment_index ASC")
    List<MeetingSegmentEntity> selectByMeetingId(@Param("meetingId") String meetingId);

    /**
     * 根据会议ID和片段序号查询片段
     * 
     * @param meetingId 会议ID
     * @param segmentIndex 片段序号
     * @return 片段实体
     */
    @Select("SELECT * FROM ai_meeting_segment WHERE meeting_id = #{meetingId} AND segment_index = #{segmentIndex}")
    MeetingSegmentEntity selectByMeetingIdAndIndex(@Param("meetingId") String meetingId, @Param("segmentIndex") Integer segmentIndex);

    /**
     * 获取会议的最新片段
     * 
     * @param meetingId 会议ID
     * @return 最新片段
     */
    @Select("SELECT * FROM ai_meeting_segment WHERE meeting_id = #{meetingId} ORDER BY segment_index DESC LIMIT 1")
    MeetingSegmentEntity selectLatestByMeetingId(@Param("meetingId") String meetingId);

    /**
     * 获取会议的片段数量
     * 
     * @param meetingId 会议ID
     * @return 片段数量
     */
    @Select("SELECT COUNT(*) FROM ai_meeting_segment WHERE meeting_id = #{meetingId}")
    Integer countByMeetingId(@Param("meetingId") String meetingId);

    /**
     * 获取会议的总字数
     * 
     * @param meetingId 会议ID
     * @return 总字数
     */
    @Select("SELECT COALESCE(SUM(word_count), 0) FROM ai_meeting_segment WHERE meeting_id = #{meetingId}")
    Integer sumWordCountByMeetingId(@Param("meetingId") String meetingId);

    /**
     * 更新片段内容
     * 
     * @param id 片段ID
     * @param content 新内容
     * @param wordCount 字数
     * @return 更新行数
     */
    @Update("UPDATE ai_meeting_segment SET content = #{content}, word_count = #{wordCount}, update_date = NOW() WHERE id = #{id}")
    int updateContent(@Param("id") String id, @Param("content") String content, @Param("wordCount") Integer wordCount);

    /**
     * 更新片段结束时间和时长
     * 
     * @param id 片段ID
     * @param endTime 结束时间
     * @param duration 时长（秒）
     * @return 更新行数
     */
    @Update("UPDATE ai_meeting_segment SET end_time = #{endTime}, duration = #{duration}, update_date = NOW() WHERE id = #{id}")
    int updateEndTimeAndDuration(@Param("id") String id, @Param("endTime") java.util.Date endTime, @Param("duration") Integer duration);

    /**
     * 根据关键词搜索片段
     * 
     * @param meetingId 会议ID
     * @param keyword 关键词
     * @return 片段列表
     */
    @Select("SELECT * FROM ai_meeting_segment WHERE meeting_id = #{meetingId} AND content LIKE CONCAT('%', #{keyword}, '%') ORDER BY segment_index ASC")
    List<MeetingSegmentEntity> searchByKeyword(@Param("meetingId") String meetingId, @Param("keyword") String keyword);

    /**
     * 获取指定时间范围内的片段
     * 
     * @param meetingId 会议ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 片段列表
     */
    @Select("SELECT * FROM ai_meeting_segment WHERE meeting_id = #{meetingId} AND start_time >= #{startTime} AND start_time <= #{endTime} ORDER BY segment_index ASC")
    List<MeetingSegmentEntity> selectByTimeRange(@Param("meetingId") String meetingId, 
                                                  @Param("startTime") java.util.Date startTime, 
                                                  @Param("endTime") java.util.Date endTime);

    /**
     * 获取会议的完整内容（所有片段内容拼接）
     * 
     * @param meetingId 会议ID
     * @return 完整内容
     */
    @Select("SELECT GROUP_CONCAT(content ORDER BY segment_index SEPARATOR '\\n') FROM ai_meeting_segment WHERE meeting_id = #{meetingId}")
    String selectFullContentByMeetingId(@Param("meetingId") String meetingId);

    /**
     * 删除会议的所有片段
     * 
     * @param meetingId 会议ID
     * @return 删除行数
     */
    @Select("DELETE FROM ai_meeting_segment WHERE meeting_id = #{meetingId}")
    int deleteByMeetingId(@Param("meetingId") String meetingId);
}
