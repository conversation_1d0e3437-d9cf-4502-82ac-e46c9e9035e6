package xiaozhi.modules.meeting.vo;

import java.io.Serializable;
import java.util.Date;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import xiaozhi.common.utils.DateUtils;

/**
 * 会议片段VO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "会议片段VO")
public class MeetingSegmentVO implements Serializable {

    @Schema(description = "片段唯一标识")
    private String id;

    @Schema(description = "会议ID")
    private String meetingId;

    @Schema(description = "片段序号")
    private Integer segmentIndex;

    @Schema(description = "片段内容")
    private String content;

    @Schema(description = "片段字数")
    private Integer wordCount;

    @Schema(description = "片段开始时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date startTime;

    @Schema(description = "片段结束时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date endTime;

    @Schema(description = "片段时长（秒）")
    private Integer duration;

    @Schema(description = "片段时长（格式化）")
    private String durationFormatted;

    @Schema(description = "说话人信息")
    private JSONObject speakerInfo;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createDate;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date updateDate;

    /**
     * 格式化时长显示
     */
    public String getDurationFormatted() {
        if (duration == null || duration <= 0) {
            return "0秒";
        }
        
        int minutes = duration / 60;
        int seconds = duration % 60;
        
        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }
}
