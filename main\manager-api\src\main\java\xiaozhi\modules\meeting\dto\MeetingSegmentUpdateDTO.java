package xiaozhi.modules.meeting.dto;

import java.io.Serializable;
import java.util.Date;


import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 更新会议片段DTO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "更新会议片段DTO")
public class MeetingSegmentUpdateDTO implements Serializable {

    @Schema(description = "片段ID", required = true)
    @NotBlank(message = "片段ID不能为空")
    private String id;

    @Schema(description = "片段内容")
    private String content;

    @Schema(description = "片段字数")
    private Integer wordCount;

    @Schema(description = "片段结束时间")
    private Date endTime;

    @Schema(description = "片段时长（秒）")
    private Integer duration;

    @Schema(description = "说话人信息（JSON格式）")
    private JSONObject speakerInfo;
}
