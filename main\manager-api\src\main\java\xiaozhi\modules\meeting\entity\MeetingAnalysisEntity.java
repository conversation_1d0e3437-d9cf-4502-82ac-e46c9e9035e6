package xiaozhi.modules.meeting.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会议分析结果实体类
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_meeting_analysis")
@Schema(description = "会议分析结果")
public class MeetingAnalysisEntity {

    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description = "分析结果唯一标识")
    private String id;

    @Schema(description = "会议ID（关联会议表）")
    private String meetingId;

    @Schema(description = "分析类型：minutes-会议纪要，summary-会议总结，suggestions-建议方案")
    private String analysisType;

    @Schema(description = "分析结果标题")
    private String title;

    @Schema(description = "分析结果内容")
    private String content;

    @Schema(description = "分析结果字数")
    private Integer wordCount;

    @Schema(description = "分析时间")
    private Date analysisTime;

    @Schema(description = "创建者ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    @Schema(description = "更新者ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;

    /**
     * 分析类型枚举
     */
    public enum AnalysisType {
        MINUTES("minutes", "会议纪要"),
        SUMMARY("summary", "会议总结"),
        SUGGESTIONS("suggestions", "建议方案");

        private final String code;
        private final String desc;

        AnalysisType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static AnalysisType fromCode(String code) {
            for (AnalysisType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
}
