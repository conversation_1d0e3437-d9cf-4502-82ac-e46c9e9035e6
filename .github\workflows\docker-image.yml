name: Docker Image CI

on:
  push:
    tags:
      - 'v*.*.*'  # 只在以 v 开头的标签推送时触发，例如 v1.0.0
  workflow_dispatch:

jobs:
  release:
    name: Release Docker images
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: write
      id-token: write
      issues: write
    steps:
      - name: Check Disk Space
        run: |
          df -h
          docker system df

      - name: Clean up Docker resources
        run: |
          docker system prune -af
          docker builder prune -af

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.TOKEN }}

      - name: Extract version from tag
        id: get_version
        run: |
          if [[ "$GITHUB_REF" =~ ^refs/tags/v([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
            echo "VERSION=${BASH_REMATCH[1]}" >> $GITHUB_ENV
            echo "IS_VERSION=true" >> $GITHUB_ENV
          else
            echo "VERSION=latest" >> $GITHUB_ENV
            echo "IS_VERSION=false" >> $GITHUB_ENV
          fi

      # 构建 xiaozhi-server 镜像
      - name: Build and push xiaozhi-server
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile-server
          push: true
          tags: |
            ${{ env.IS_VERSION == 'true' && format('ghcr.io/{0}:server_{1},ghcr.io/{0}:server_latest', github.repository, env.VERSION) || format('ghcr.io/{0}:server_latest', github.repository) }}
          platforms: linux/amd64,linux/arm64

      # 构建 manager-api 镜像
      - name: Build and push manager-web
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile-web
          push: true
          tags: |
            ${{ env.IS_VERSION == 'true' && format('ghcr.io/{0}:web_{1},ghcr.io/{0}:web_latest', github.repository, env.VERSION) || format('ghcr.io/{0}:web_latest', github.repository) }}
          platforms: linux/amd64,linux/arm64