package xiaozhi.modules.meeting.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.PageData;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.meeting.dao.MeetingDao;
import xiaozhi.modules.meeting.dao.MeetingSegmentDao;
import xiaozhi.modules.meeting.dto.MeetingCreateDTO;
import xiaozhi.modules.meeting.dto.MeetingPageDTO;
import xiaozhi.modules.meeting.dto.MeetingUpdateDTO;
import xiaozhi.modules.meeting.entity.MeetingEntity;
import xiaozhi.modules.meeting.service.MeetingAnalysisService;
import xiaozhi.modules.meeting.service.MeetingQaService;
import xiaozhi.modules.meeting.service.MeetingSegmentService;
import xiaozhi.modules.meeting.service.MeetingService;
import xiaozhi.modules.meeting.vo.MeetingDetailVO;
import xiaozhi.modules.meeting.vo.MeetingVO;

/**
 * 会议管理服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@Service
public class MeetingServiceImpl extends BaseServiceImpl<MeetingDao, MeetingEntity> implements MeetingService {

    @Autowired
    private MeetingSegmentService meetingSegmentService;

    @Autowired
    private MeetingAnalysisService meetingAnalysisService;

    @Autowired
    private MeetingQaService meetingQaService;

    @Autowired
    private MeetingSegmentDao meetingSegmentDao;

    @Override
    public PageData<MeetingVO> page(MeetingPageDTO dto) {
        // 构建查询条件
        QueryWrapper<MeetingEntity> wrapper = new QueryWrapper<>();
        
        if (dto.getUserId() != null) {
            wrapper.eq("user_id", dto.getUserId());
        }
        
        if (dto.getDeviceId() != null && !dto.getDeviceId().trim().isEmpty()) {
            wrapper.eq("device_id", dto.getDeviceId());
        }
        
        if (dto.getStatus() != null && !dto.getStatus().trim().isEmpty()) {
            wrapper.eq("status", dto.getStatus());
        }
        
        if (dto.getKeyword() != null && !dto.getKeyword().trim().isEmpty()) {
            wrapper.like("title", dto.getKeyword());
        }
        
        if (dto.getStartTime() != null) {
            wrapper.ge("start_time", dto.getStartTime());
        }
        
        if (dto.getEndTime() != null) {
            wrapper.le("start_time", dto.getEndTime());
        }
        
        // 按开始时间倒序排列
        wrapper.orderByDesc("start_time");

        // 构建分页参数
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.PAGE, dto.getPage());
        params.put(Constant.LIMIT, dto.getLimit());

        // 分页查询
        IPage<MeetingEntity> page = baseDao.selectPage(getPage(params, "start_time", false), wrapper);
        
        // 转换为VO
        List<MeetingVO> voList = page.getRecords().stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingVO.class))
                .collect(Collectors.toList());
        
        return new PageData<>(voList, page.getTotal());
    }

    @Override
    public MeetingDetailVO getDetail(String id) {
        MeetingEntity entity = baseDao.selectById(id);
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        MeetingDetailVO vo = ConvertUtils.sourceToTarget(entity, MeetingDetailVO.class);
        
        // 获取会议片段
        vo.setSegments(meetingSegmentService.getByMeetingId(id));
        
        // 获取分析结果
        vo.setAnalysisResults(meetingAnalysisService.getByMeetingId(id));
        
        // 获取问答记录
        vo.setQaRecords(meetingQaService.getByMeetingId(id));
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MeetingVO create(MeetingCreateDTO dto) {
        MeetingEntity entity = ConvertUtils.sourceToTarget(dto, MeetingEntity.class);
        
        // 设置默认值
        entity.setStatus(MeetingEntity.Status.ACTIVE.getCode());
        entity.setStartTime(new Date());
        entity.setTotalDuration(0);
        entity.setTotalSegments(0);
        entity.setTotalWords(0);
        
        // 保存会议记录
        insert(entity);
        
        log.info("创建会议成功，会议ID: {}, 标题: {}", entity.getId(), entity.getTitle());
        
        return ConvertUtils.sourceToTarget(entity, MeetingVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MeetingUpdateDTO dto) {
        MeetingEntity entity = baseDao.selectById(dto.getId());
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        // 更新字段
        if (dto.getTitle() != null) {
            entity.setTitle(dto.getTitle());
        }
        if (dto.getStatus() != null) {
            entity.setStatus(dto.getStatus());
        }
        if (dto.getEndTime() != null) {
            entity.setEndTime(dto.getEndTime());
        }
        if (dto.getTotalDuration() != null) {
            entity.setTotalDuration(dto.getTotalDuration());
        }
        
        updateById(entity);
        
        log.info("更新会议成功，会议ID: {}", dto.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        MeetingEntity entity = baseDao.selectById(id);
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        // 删除会议记录（级联删除片段、分析结果、问答记录）
        baseDao.deleteById(id);
        
        log.info("删除会议成功，会议ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(String[] ids) {
        for (String id : ids) {
            delete(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endMeeting(String id) {
        MeetingEntity entity = baseDao.selectById(id);
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        // 更新会议状态为已完成
        entity.setStatus(MeetingEntity.Status.COMPLETED.getCode());
        // entity.setEndTime(endTime);
        // entity.setTotalDuration(totalDuration);
        
        // 更新统计信息
        Integer totalSegments = meetingSegmentDao.countByMeetingId(id);
        Integer totalWords = meetingSegmentDao.sumWordCountByMeetingId(id);
        entity.setTotalSegments(totalSegments);
        entity.setTotalWords(totalWords);
        
        updateById(entity);
        
        log.info("结束会议成功，会议ID: {}, 总片段: {}, 总字数: {}", 
                id, totalSegments, totalWords);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelMeeting(String id) {
        MeetingEntity entity = baseDao.selectById(id);
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        // 更新会议状态为已取消
        entity.setStatus(MeetingEntity.Status.CANCELLED.getCode());
        entity.setEndTime(new Date());
        
        updateById(entity);
        
        log.info("取消会议成功，会议ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatistics(String id, Integer totalSegments, Integer totalWords) {
        baseDao.updateStatistics(id, totalSegments, totalWords);
        
        log.debug("更新会议统计信息，会议ID: {}, 总片段: {}, 总字数: {}", id, totalSegments, totalWords);
    }

    @Override
    public List<MeetingVO> getByUserId(Long userId) {
        List<MeetingEntity> entities = baseDao.selectByUserId(userId);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingVO> getByDeviceId(String deviceId) {
        List<MeetingEntity> entities = baseDao.selectByDeviceId(deviceId);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingVO> getByStatus(String status) {
        List<MeetingEntity> entities = baseDao.selectByStatus(status);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingVO> getByUserIdAndStatus(Long userId, String status) {
        List<MeetingEntity> entities = baseDao.selectByUserIdAndStatus(userId, status);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingVO> getByTimeRange(Long userId, Date startTime, Date endTime) {
        List<MeetingEntity> entities = baseDao.selectByTimeRange(userId, startTime, endTime);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingVO> searchByKeyword(Long userId, String keyword) {
        List<MeetingEntity> entities = baseDao.searchByKeyword(userId, keyword);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public Integer countByUserId(Long userId) {
        return baseDao.countByUserId(userId);
    }

    @Override
    public Integer countByUserIdAndStatus(Long userId, String status) {
        return baseDao.countByUserIdAndStatus(userId, status);
    }

    @Override
    public String getFullContent(String id) {
        return meetingSegmentDao.selectFullContentByMeetingId(id);
    }
}
