package xiaozhi.modules.meeting.service;

import java.util.Date;
import java.util.List;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.meeting.dto.MeetingSegmentCreateDTO;
import xiaozhi.modules.meeting.dto.MeetingSegmentUpdateDTO;
import xiaozhi.modules.meeting.entity.MeetingSegmentEntity;
import xiaozhi.modules.meeting.vo.MeetingSegmentVO;

/**
 * 会议片段服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface MeetingSegmentService extends BaseService<MeetingSegmentEntity> {

    /**
     * 创建会议片段
     * 
     * @param dto 创建会议片段参数
     * @return 会议片段信息
     */
    MeetingSegmentVO create(MeetingSegmentCreateDTO dto);

    /**
     * 更新会议片段
     * 
     * @param dto 更新会议片段参数
     */
    void update(MeetingSegmentUpdateDTO dto);

    /**
     * 删除会议片段
     * 
     * @param id 片段ID
     */
    void delete(String id);

    /**
     * 根据会议ID获取所有片段
     * 
     * @param meetingId 会议ID
     * @return 片段列表
     */
    List<MeetingSegmentVO> getByMeetingId(String meetingId);

    /**
     * 根据会议ID和片段序号获取片段
     * 
     * @param meetingId 会议ID
     * @param segmentIndex 片段序号
     * @return 片段信息
     */
    MeetingSegmentVO getByMeetingIdAndIndex(String meetingId, Integer segmentIndex);

    /**
     * 获取会议的最新片段
     * 
     * @param meetingId 会议ID
     * @return 最新片段
     */
    MeetingSegmentVO getLatestByMeetingId(String meetingId);

    /**
     * 更新片段内容
     * 
     * @param id 片段ID
     * @param content 新内容
     * @param wordCount 字数
     */
    void updateContent(String id, String content, Integer wordCount);

    /**
     * 更新片段结束时间和时长
     * 
     * @param id 片段ID
     * @param endTime 结束时间
     * @param duration 时长（秒）
     */
    void updateEndTimeAndDuration(String id, Date endTime, Integer duration);

    /**
     * 根据关键词搜索片段
     * 
     * @param meetingId 会议ID
     * @param keyword 关键词
     * @return 片段列表
     */
    List<MeetingSegmentVO> searchByKeyword(String meetingId, String keyword);

    /**
     * 获取指定时间范围内的片段
     * 
     * @param meetingId 会议ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 片段列表
     */
    List<MeetingSegmentVO> getByTimeRange(String meetingId, Date startTime, Date endTime);

    /**
     * 获取会议的完整内容（所有片段内容拼接）
     * 
     * @param meetingId 会议ID
     * @return 完整内容
     */
    String getFullContentByMeetingId(String meetingId);

    /**
     * 统计会议的片段数量
     * 
     * @param meetingId 会议ID
     * @return 片段数量
     */
    Integer countByMeetingId(String meetingId);

    /**
     * 统计会议的总字数
     * 
     * @param meetingId 会议ID
     * @return 总字数
     */
    Integer sumWordCountByMeetingId(String meetingId);

    /**
     * 删除会议的所有片段
     * 
     * @param meetingId 会议ID
     */
    void deleteByMeetingId(String meetingId);

    MeetingSegmentVO getById(String id);
}
