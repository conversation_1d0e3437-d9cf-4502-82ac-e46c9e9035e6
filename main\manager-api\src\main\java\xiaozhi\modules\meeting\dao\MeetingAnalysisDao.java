package xiaozhi.modules.meeting.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.meeting.entity.MeetingAnalysisEntity;

/**
 * 会议分析结果DAO接口
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Mapper
public interface MeetingAnalysisDao extends BaseMapper<MeetingAnalysisEntity> {

    /**
     * 根据会议ID查询所有分析结果
     * 
     * @param meetingId 会议ID
     * @return 分析结果列表
     */
    @Select("SELECT * FROM ai_meeting_analysis WHERE meeting_id = #{meetingId} ORDER BY analysis_time DESC")
    List<MeetingAnalysisEntity> selectByMeetingId(@Param("meetingId") String meetingId);

    /**
     * 根据会议ID和分析类型查询分析结果
     * 
     * @param meetingId 会议ID
     * @param analysisType 分析类型
     * @return 分析结果
     */
    @Select("SELECT * FROM ai_meeting_analysis WHERE meeting_id = #{meetingId} AND analysis_type = #{analysisType} ORDER BY analysis_time DESC LIMIT 1")
    MeetingAnalysisEntity selectByMeetingIdAndType(@Param("meetingId") String meetingId, @Param("analysisType") String analysisType);

    /**
     * 根据分析类型查询分析结果列表
     * 
     * @param analysisType 分析类型
     * @return 分析结果列表
     */
    @Select("SELECT * FROM ai_meeting_analysis WHERE analysis_type = #{analysisType} ORDER BY analysis_time DESC")
    List<MeetingAnalysisEntity> selectByAnalysisType(@Param("analysisType") String analysisType);

    /**
     * 根据用户ID查询分析结果（通过关联会议表）
     * 
     * @param userId 用户ID
     * @return 分析结果列表
     */
    @Select("SELECT a.* FROM ai_meeting_analysis a " +
            "INNER JOIN ai_meeting m ON a.meeting_id = m.id " +
            "WHERE m.user_id = #{userId} " +
            "ORDER BY a.analysis_time DESC")
    List<MeetingAnalysisEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和分析类型查询分析结果
     * 
     * @param userId 用户ID
     * @param analysisType 分析类型
     * @return 分析结果列表
     */
    @Select("SELECT a.* FROM ai_meeting_analysis a " +
            "INNER JOIN ai_meeting m ON a.meeting_id = m.id " +
            "WHERE m.user_id = #{userId} AND a.analysis_type = #{analysisType} " +
            "ORDER BY a.analysis_time DESC")
    List<MeetingAnalysisEntity> selectByUserIdAndType(@Param("userId") Long userId, @Param("analysisType") String analysisType);

    /**
     * 统计会议的分析结果数量
     * 
     * @param meetingId 会议ID
     * @return 分析结果数量
     */
    @Select("SELECT COUNT(*) FROM ai_meeting_analysis WHERE meeting_id = #{meetingId}")
    Integer countByMeetingId(@Param("meetingId") String meetingId);

    /**
     * 统计指定类型的分析结果数量
     * 
     * @param meetingId 会议ID
     * @param analysisType 分析类型
     * @return 分析结果数量
     */
    @Select("SELECT COUNT(*) FROM ai_meeting_analysis WHERE meeting_id = #{meetingId} AND analysis_type = #{analysisType}")
    Integer countByMeetingIdAndType(@Param("meetingId") String meetingId, @Param("analysisType") String analysisType);

    /**
     * 根据关键词搜索分析结果
     * 
     * @param keyword 关键词
     * @return 分析结果列表
     */
    @Select("SELECT * FROM ai_meeting_analysis WHERE title LIKE CONCAT('%', #{keyword}, '%') OR content LIKE CONCAT('%', #{keyword}, '%') ORDER BY analysis_time DESC")
    List<MeetingAnalysisEntity> searchByKeyword(@Param("keyword") String keyword);

    /**
     * 根据用户ID和关键词搜索分析结果
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 分析结果列表
     */
    @Select("SELECT a.* FROM ai_meeting_analysis a " +
            "INNER JOIN ai_meeting m ON a.meeting_id = m.id " +
            "WHERE m.user_id = #{userId} AND (a.title LIKE CONCAT('%', #{keyword}, '%') OR a.content LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY a.analysis_time DESC")
    List<MeetingAnalysisEntity> searchByUserIdAndKeyword(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 删除会议的所有分析结果
     * 
     * @param meetingId 会议ID
     * @return 删除行数
     */
    @Select("DELETE FROM ai_meeting_analysis WHERE meeting_id = #{meetingId}")
    int deleteByMeetingId(@Param("meetingId") String meetingId);
}
