package xiaozhi.modules.meeting.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.annotation.LogOperation;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.Result;
import xiaozhi.common.utils.ResultUtils;
import xiaozhi.modules.meeting.dto.MeetingAnalysisCreateDTO;
import xiaozhi.modules.meeting.service.MeetingAnalysisService;
import xiaozhi.modules.meeting.vo.MeetingAnalysisVO;
import xiaozhi.modules.security.user.SecurityUser;

/**
 * 会议分析结果管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/meeting/analysis")
@Tag(name = "会议分析管理")
public class MeetingAnalysisController {

    private final MeetingAnalysisService meetingAnalysisService;

    @GetMapping("/meeting/{meetingId}")
    @Operation(summary = "获取会议的所有分析结果")
    @RequiresPermissions("sys:role:normal")
    public Result<List<MeetingAnalysisVO>> getByMeetingId(@PathVariable("meetingId") String meetingId) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        List<MeetingAnalysisVO> analyses = meetingAnalysisService.getByMeetingId(meetingId);
        
        log.info("用户{}获取会议分析结果列表，会议ID: {}, 分析结果数量: {}", user.getId(), meetingId, analyses.size());
        
        return ResultUtils.success(analyses);
    }

    @GetMapping("/meeting/{meetingId}/type/{analysisType}")
    @Operation(summary = "获取会议指定类型的分析结果")
    @RequiresPermissions("sys:role:normal")
    @Parameter(name = "analysisType", description = "分析类型：minutes-会议纪要，summary-会议总结，suggestions-会议建议")
    public Result<MeetingAnalysisVO> getByMeetingIdAndType(
            @PathVariable("meetingId") String meetingId,
            @PathVariable("analysisType") String analysisType) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        MeetingAnalysisVO analysis = meetingAnalysisService.getByMeetingIdAndType(meetingId, analysisType);
        
        log.info("用户{}获取会议分析结果，会议ID: {}, 分析类型: {}", user.getId(), meetingId, analysisType);
        
        return ResultUtils.success(analysis);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取分析结果详情")
    @RequiresPermissions("sys:role:normal")
    public Result<MeetingAnalysisVO> getById(@PathVariable("id") String id) {
        MeetingAnalysisVO analysis = meetingAnalysisService.getById(id);
        
        log.info("获取会议分析结果详情，分析ID: {}",  id);
        
        return ResultUtils.success(analysis);
    }

    @PostMapping
    @Operation(summary = "创建会议分析结果")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("创建会议分析结果")
    public Result<MeetingAnalysisVO> create(@RequestBody @Valid MeetingAnalysisCreateDTO dto) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        MeetingAnalysisVO analysis = meetingAnalysisService.create(dto);
        
        log.info("用户{}创建会议分析结果成功，分析ID: {}, 会议ID: {}, 分析类型: {}", 
                user.getId(), analysis.getId(), analysis.getMeetingId(), analysis.getAnalysisType());
        
        return ResultUtils.success(analysis);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除会议分析结果")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("删除会议分析结果")
    public Result<Void> delete(@PathVariable("id") String id) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        meetingAnalysisService.delete(id);
        
        log.info("用户{}删除会议分析结果，分析ID: {}", user.getId(), id);
        
        return ResultUtils.success(null);
    }



    @GetMapping("/search")
    @Operation(summary = "搜索会议分析结果")
    @RequiresPermissions("sys:role:normal")
    public Result<List<MeetingAnalysisVO>> search(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "20") Integer limit) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        List<MeetingAnalysisVO> analyses = meetingAnalysisService.searchByUserIdAndKeyword(user.getId(), keyword);
        
        log.info("用户{}搜索会议分析结果，关键词: {}, 结果数量: {}", user.getId(), keyword, analyses.size());
        
        return ResultUtils.success(analyses);
    }

    @GetMapping("/types")
    @Operation(summary = "获取支持的分析类型列表")
    @RequiresPermissions("sys:role:normal")
    public Result<List<String>> getSupportedTypes() {
        List<String> types = List.of("minutes", "summary", "suggestions");
        
        log.info("获取支持的分析类型列表");
        
        return ResultUtils.success(types);
    }
}
