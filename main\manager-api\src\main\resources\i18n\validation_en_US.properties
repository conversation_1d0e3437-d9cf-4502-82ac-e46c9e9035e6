#English
id.require=ID can not be empty
id.null=ID has to be empty

sort.number=The sort value cannot be less than 0
page.number=The page value cannot be less than 0
limit.number=The limit value cannot be less than 0

sysdict.type.require=The dictionary type cannot be empty
sysdict.name.require=The dictionary name cannot be empty
sysdict.label.require=Dictionary tag cannot be empty

sysparams.paramcode.require=Parameter encoding cannot be empty
sysparams.paramvalue.require=Parameter values cannot be empty
sysparams.valuetype.require=Value type cannot be empty
sysparams.valuetype.pattern=Value type must be string, number, boolean or array

sysuser.username.require=The username cannot be empty
sysuser.password.require=The password cannot be empty
sysuser.realname.require=The realname cannot be empty
sysuser.gender.range=Gender ranges from 0 to 2
sysuser.email.error=Incorrect email format
sysuser.deptId.require=Departments cannot be empty
sysuser.status.range=State ranges from 0 to 1
sysuser.captcha.require=The captcha cannot be empty
sysuser.uuid.require=The unique identifier cannot be empty

timbre.languages.require=The language of the timbre cannot be empty
timbre.name.require=The name of the timbre cannot be empty
timbre.ttsModelId.require=The TTS model ID of the timbre cannot be empty
timbre.ttsVoice.require=The TTS voice of the timbre cannot be empty

ota.device.not.found=Device not found
ota.device.need.bind={0}