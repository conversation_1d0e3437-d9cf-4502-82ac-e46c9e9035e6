package xiaozhi.modules.meeting.dto;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 创建会议DTO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "创建会议DTO")
public class MeetingCreateDTO implements Serializable {

    @Schema(description = "设备ID", required = true)
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;

    @Schema(description = "会议标题", required = true)
    @NotBlank(message = "会议标题不能为空")
    private String title;

    @Schema(description = "用户ID", hidden = true)
    private Long userId;
}
