package xiaozhi.modules.meeting.service;

import java.util.List;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.meeting.dto.MeetingAnalysisCreateDTO;
import xiaozhi.modules.meeting.entity.MeetingAnalysisEntity;
import xiaozhi.modules.meeting.vo.MeetingAnalysisVO;

/**
 * 会议分析结果服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface MeetingAnalysisService extends BaseService<MeetingAnalysisEntity> {

    MeetingAnalysisVO getById(String id);


    /**
     * 创建会议分析结果
     * 
     * @param dto 创建会议分析结果参数
     * @return 会议分析结果信息
     */
    MeetingAnalysisVO create(MeetingAnalysisCreateDTO dto);

    /**
     * 删除会议分析结果
     * 
     * @param id 分析结果ID
     */
    void delete(String id);

    /**
     * 根据会议ID获取所有分析结果
     * 
     * @param meetingId 会议ID
     * @return 分析结果列表
     */
    List<MeetingAnalysisVO> getByMeetingId(String meetingId);

    /**
     * 根据会议ID和分析类型获取分析结果
     * 
     * @param meetingId 会议ID
     * @param analysisType 分析类型
     * @return 分析结果
     */
    MeetingAnalysisVO getByMeetingIdAndType(String meetingId, String analysisType);

    /**
     * 根据分析类型获取分析结果列表
     * 
     * @param analysisType 分析类型
     * @return 分析结果列表
     */
    List<MeetingAnalysisVO> getByAnalysisType(String analysisType);

    /**
     * 根据用户ID获取分析结果
     * 
     * @param userId 用户ID
     * @return 分析结果列表
     */
    List<MeetingAnalysisVO> getByUserId(Long userId);

    /**
     * 根据用户ID和分析类型获取分析结果
     * 
     * @param userId 用户ID
     * @param analysisType 分析类型
     * @return 分析结果列表
     */
    List<MeetingAnalysisVO> getByUserIdAndType(Long userId, String analysisType);

    /**
     * 根据关键词搜索分析结果
     * 
     * @param keyword 关键词
     * @return 分析结果列表
     */
    List<MeetingAnalysisVO> searchByKeyword(String keyword);

    /**
     * 根据用户ID和关键词搜索分析结果
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 分析结果列表
     */
    List<MeetingAnalysisVO> searchByUserIdAndKeyword(Long userId, String keyword);

    /**
     * 统计会议的分析结果数量
     * 
     * @param meetingId 会议ID
     * @return 分析结果数量
     */
    Integer countByMeetingId(String meetingId);

    /**
     * 统计指定类型的分析结果数量
     * 
     * @param meetingId 会议ID
     * @param analysisType 分析类型
     * @return 分析结果数量
     */
    Integer countByMeetingIdAndType(String meetingId, String analysisType);

    /**
     * 删除会议的所有分析结果
     * 
     * @param meetingId 会议ID
     */
    void deleteByMeetingId(String meetingId);
}
