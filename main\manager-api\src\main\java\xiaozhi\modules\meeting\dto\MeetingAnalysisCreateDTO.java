package xiaozhi.modules.meeting.dto;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 创建会议分析结果DTO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "创建会议分析结果DTO")
public class MeetingAnalysisCreateDTO implements Serializable {

    @Schema(description = "会议ID", required = true)
    @NotBlank(message = "会议ID不能为空")
    private String meetingId;

    @Schema(description = "分析类型：minutes-会议纪要，summary-会议总结，suggestions-建议方案", required = true)
    @NotBlank(message = "分析类型不能为空")
    private String analysisType;

    @Schema(description = "分析结果标题")
    private String title;

    @Schema(description = "分析结果内容", required = true)
    @NotBlank(message = "分析结果内容不能为空")
    private String content;

    @Schema(description = "分析结果字数")
    private Integer wordCount;
}
