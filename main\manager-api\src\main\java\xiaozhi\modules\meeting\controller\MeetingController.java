package xiaozhi.modules.meeting.controller;

import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.annotation.LogOperation;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.page.PageData;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.Result;
import xiaozhi.common.utils.ResultUtils;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.modules.meeting.dto.MeetingCreateDTO;
import xiaozhi.modules.meeting.dto.MeetingPageDTO;
import xiaozhi.modules.meeting.dto.MeetingUpdateDTO;
import xiaozhi.modules.meeting.service.MeetingService;
import xiaozhi.modules.meeting.vo.MeetingDetailVO;
import xiaozhi.modules.meeting.vo.MeetingVO;
import xiaozhi.modules.security.user.SecurityUser;

/**
 * 会议管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/meeting")
@Tag(name = "会议管理")
public class MeetingController {

    private final MeetingService meetingService;

    @GetMapping("/page")
    @Operation(summary = "分页查询会议列表")
    @RequiresPermissions("sys:role:normal")
    @Parameters({
        @Parameter(name = Constant.PAGE, description = "当前页码，从1开始", required = true),
        @Parameter(name = Constant.LIMIT, description = "每页显示记录数", required = true),
        @Parameter(name = "deviceId", description = "设备ID"),
        @Parameter(name = "status", description = "会议状态：ACTIVE-进行中，COMPLETED-已完成，CANCELLED-已取消"),
        @Parameter(name = "keyword", description = "关键词搜索（标题）"),
        @Parameter(name = "startTime", description = "开始时间（查询范围）"),
        @Parameter(name = "endTime", description = "结束时间（查询范围）")
    })
    public Result<PageData<MeetingVO>> page(@Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        // 参数验证
        ValidatorUtils.validateEntity(params);
        
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        // 构建查询DTO
        MeetingPageDTO dto = new MeetingPageDTO();
        dto.setUserId(user.getId());
        dto.setPage((String) params.get(Constant.PAGE));
        dto.setLimit((String) params.get(Constant.LIMIT));
        dto.setDeviceId((String) params.get("deviceId"));
        dto.setStatus((String) params.get("status"));
        dto.setKeyword((String) params.get("keyword"));
        
        // 处理时间参数
        if (params.get("startTime") != null) {
            dto.setStartTime((java.util.Date) params.get("startTime"));
        }
        if (params.get("endTime") != null) {
            dto.setEndTime((java.util.Date) params.get("endTime"));
        }
        
        PageData<MeetingVO> page = meetingService.page(dto);
        
        log.info("用户{}查询会议列表，共{}条记录", user.getId(), page.getTotal());
        
        return ResultUtils.success(page);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取会议详情")
    @RequiresPermissions("sys:role:normal")
    public Result<MeetingDetailVO> getDetail(@PathVariable("id") String id) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        MeetingDetailVO detail = meetingService.getDetail(id);
        
        log.info("用户{}查看会议详情，会议ID: {}", user.getId(), id);
        
        return ResultUtils.success(detail);
    }

    @PostMapping
    @Operation(summary = "创建会议")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("创建会议")
    public Result<MeetingVO> create(@RequestBody @Valid MeetingCreateDTO dto) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        dto.setUserId(user.getId());
        
        MeetingVO meeting = meetingService.create(dto);
        
        log.info("用户{}创建会议成功，会议ID: {}, 标题: {}", user.getId(), meeting.getId(), meeting.getTitle());
        
        return ResultUtils.success(meeting);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新会议信息")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("更新会议信息")
    public Result<Void> update(@PathVariable("id") String id, @RequestBody @Valid MeetingUpdateDTO dto) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        dto.setId(id);
        
        meetingService.update(dto);
        
        log.info("用户{}更新会议信息成功，会议ID: {}", user.getId(), id);
        
        return ResultUtils.success(null);
    }

    @PutMapping("/{id}/end")
    @Operation(summary = "结束会议")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("结束会议")
    public Result<Void> endMeeting(@PathVariable("id") String id) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        meetingService.endMeeting(id);
        
        log.info("用户{}结束会议，会议ID: {}", user.getId(), id);
        
        return ResultUtils.success(null);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除会议")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("删除会议")
    public Result<Void> delete(@PathVariable("id") String id) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        meetingService.delete(id);
        
        log.info("用户{}删除会议，会议ID: {}", user.getId(), id);
        
        return ResultUtils.success(null);
    }

    @GetMapping("/{id}/content")
    @Operation(summary = "获取会议完整内容")
    @RequiresPermissions("sys:role:normal")
    public Result<String> getFullContent(@PathVariable("id") String id) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        String content = meetingService.getFullContent(id);
        
        log.info("用户{}获取会议完整内容，会议ID: {}", user.getId(), id);
        
        return ResultUtils.success(content);
    }


    @GetMapping("/recent")
    @Operation(summary = "获取最近会议列表（分页）")
    @RequiresPermissions("sys:role:normal")
    public Result<PageData<MeetingVO>> getRecentMeetings(@Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        // 参数验证
        ValidatorUtils.validateEntity(params);

        // 获取当前用户
        UserDetail user = SecurityUser.getUser();

        // 构建查询DTO，只查询最近的会议（按时间倒序）
        MeetingPageDTO dto = new MeetingPageDTO();
        dto.setUserId(user.getId());
        dto.setPage((String) params.getOrDefault(Constant.PAGE, "1"));
        dto.setLimit((String) params.getOrDefault(Constant.LIMIT, "10"));

        // 可选的设备ID过滤
        if (params.containsKey("deviceId")) {
            dto.setDeviceId((String) params.get("deviceId"));
        }

        // 可选的状态过滤
        if (params.containsKey("status")) {
            dto.setStatus((String) params.get("status"));
        }

        PageData<MeetingVO> page = meetingService.page(dto);

        log.info("用户{}获取最近会议列表，第{}页，每页{}条，共{}条记录",
                user.getId(), dto.getPage(), dto.getLimit(), page.getTotal());

        return ResultUtils.success(page);
    }

}
