package xiaozhi.modules.meeting.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.annotation.LogOperation;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.Result;
import xiaozhi.common.utils.ResultUtils;
import xiaozhi.modules.meeting.dto.MeetingSegmentCreateDTO;
import xiaozhi.modules.meeting.dto.MeetingSegmentUpdateDTO;
import xiaozhi.modules.meeting.service.MeetingSegmentService;
import xiaozhi.modules.meeting.vo.MeetingSegmentVO;
import xiaozhi.modules.security.user.SecurityUser;

/**
 * 会议片段管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/meeting/segment")
@Tag(name = "会议片段管理")
public class MeetingSegmentController {

    private final MeetingSegmentService meetingSegmentService;

    @GetMapping("/meeting/{meetingId}")
    @Operation(summary = "获取会议的所有片段")
    @RequiresPermissions("sys:role:normal")
    public Result<List<MeetingSegmentVO>> getByMeetingId(@PathVariable("meetingId") String meetingId) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        List<MeetingSegmentVO> segments = meetingSegmentService.getByMeetingId(meetingId);
        
        log.info("用户{}获取会议片段列表，会议ID: {}, 片段数量: {}", user.getId(), meetingId, segments.size());
        
        return ResultUtils.success(segments);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取片段详情")
    @RequiresPermissions("sys:role:normal")
    public Result<MeetingSegmentVO> getById(@PathVariable("id") String id) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        MeetingSegmentVO segment = meetingSegmentService.getById(id);
        
        log.info("用户{}获取会议片段详情，片段ID: {}", user.getId(), id);
        
        return ResultUtils.success(segment);
    }

    @PostMapping
    @Operation(summary = "创建会议片段")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("创建会议片段")
    public Result<MeetingSegmentVO> create(@RequestBody @Valid MeetingSegmentCreateDTO dto) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        MeetingSegmentVO segment = meetingSegmentService.create(dto);
        
        log.info("用户{}创建会议片段成功，片段ID: {}, 会议ID: {}", 
                user.getId(), segment.getId(), segment.getMeetingId());
        
        return ResultUtils.success(segment);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新会议片段")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("更新会议片段")
    public Result<Void> update(@PathVariable("id") String id, @RequestBody @Valid MeetingSegmentUpdateDTO dto) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        dto.setId(id);
        
        meetingSegmentService.update(dto);
        
        log.info("用户{}更新会议片段成功，片段ID: {}", user.getId(), id);
        
        return ResultUtils.success(null);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除会议片段")
    @RequiresPermissions("sys:role:normal")
    @LogOperation("删除会议片段")
    public Result<Void> delete(@PathVariable("id") String id) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        meetingSegmentService.delete(id);
        
        log.info("用户{}删除会议片段，片段ID: {}", user.getId(), id);
        
        return ResultUtils.success(null);
    }





    @GetMapping("/meeting/{meetingId}/content")
    @Operation(summary = "获取会议完整内容（按片段组织）")
    @RequiresPermissions("sys:role:normal")
    public Result<String> getFullContentByMeetingId(@PathVariable("meetingId") String meetingId) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        
        String content = meetingSegmentService.getFullContentByMeetingId(meetingId);
        
        log.info("用户{}获取会议完整内容，会议ID: {}", user.getId(), meetingId);
        
        return ResultUtils.success(content);
    }


}
