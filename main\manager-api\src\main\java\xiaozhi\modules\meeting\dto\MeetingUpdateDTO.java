package xiaozhi.modules.meeting.dto;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 更新会议DTO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "更新会议DTO")
public class MeetingUpdateDTO implements Serializable {

    @Schema(description = "会议ID", required = true)
    @NotBlank(message = "会议ID不能为空")
    private String id;

    @Schema(description = "会议标题")
    private String title;

    @Schema(description = "会议状态：ACTIVE-进行中，COMPLETED-已完成，CANCELLED-已取消")
    private String status;

    @Schema(description = "会议结束时间")
    private Date endTime;

    @Schema(description = "会议总时长（秒）")
    private Integer totalDuration;
}
