package xiaozhi.modules.meeting.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.meeting.dao.MeetingQaDao;
import xiaozhi.modules.meeting.dto.MeetingQaCreateDTO;
import xiaozhi.modules.meeting.entity.MeetingQaEntity;
import xiaozhi.modules.meeting.service.MeetingQaService;
import xiaozhi.modules.meeting.vo.MeetingQaVO;

/**
 * 会议问答记录服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@Service
public class MeetingQaServiceImpl extends BaseServiceImpl<MeetingQaDao, MeetingQaEntity> implements MeetingQaService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MeetingQaVO create(MeetingQaCreateDTO dto) {
        MeetingQaEntity entity = ConvertUtils.sourceToTarget(dto, MeetingQaEntity.class);
        
        // 保存问答记录
        insert(entity);
        
        log.info("创建会议问答记录成功，问答ID: {}, 会议ID: {}", 
                entity.getId(), entity.getMeetingId());
        
        return ConvertUtils.sourceToTarget(entity, MeetingQaVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        MeetingQaEntity entity = baseDao.selectById(id);
        if (entity == null) {
            throw new RenException(ErrorCode.DB_RECORD_NOT_EXISTS);
        }
        
        baseDao.deleteById(id);
        
        log.info("删除会议问答记录成功，问答ID: {}", id);
    }

    @Override
    public List<MeetingQaVO> getByMeetingId(String meetingId) {
        List<MeetingQaEntity> entities = baseDao.selectByMeetingId(meetingId);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingQaVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingQaVO> getByUserId(Long userId) {
        List<MeetingQaEntity> entities = baseDao.selectByUserId(userId);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingQaVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingQaVO> searchByQuestion(String meetingId, String keyword) {
        List<MeetingQaEntity> entities = baseDao.searchByQuestion(meetingId, keyword);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingQaVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingQaVO> searchByAnswer(String meetingId, String keyword) {
        List<MeetingQaEntity> entities = baseDao.searchByAnswer(meetingId, keyword);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingQaVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingQaVO> searchByKeyword(String meetingId, String keyword) {
        List<MeetingQaEntity> entities = baseDao.searchByKeyword(meetingId, keyword);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingQaVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<MeetingQaVO> searchByUserIdAndKeyword(Long userId, String keyword) {
        List<MeetingQaEntity> entities = baseDao.searchByUserIdAndKeyword(userId, keyword);
        return entities.stream()
                .map(entity -> ConvertUtils.sourceToTarget(entity, MeetingQaVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public Integer countByMeetingId(String meetingId) {
        return baseDao.countByMeetingId(meetingId);
    }

    @Override
    public Integer countByUserId(Long userId) {
        return baseDao.countByUserId(userId);
    }

    @Override
    public Double getAverageResponseTime(String meetingId) {
        return baseDao.getAverageResponseTime(meetingId);
    }

    @Override
    public Double getAverageConfidenceScore(String meetingId) {
        return baseDao.getAverageConfidenceScore(meetingId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMeetingId(String meetingId) {
        int result = baseDao.deleteByMeetingId(meetingId);
        
        log.info("删除会议所有问答记录成功，会议ID: {}, 删除数量: {}", meetingId, result);
    }
}
