-- 会议系统数据库表创建脚本
-- 本文件用于创建会议录音和智能问答系统相关表，无需手动执行，在项目启动时会自动执行
-- -------------------------------------------------------

-- 会议记录表
DROP TABLE IF EXISTS `ai_meeting`;
CREATE TABLE `ai_meeting` (
    `id` VARCHAR(32) NOT NULL COMMENT '会议唯一标识',
    `user_id` BIGINT NOT NULL COMMENT '用户ID（关联用户表）',
    `device_id` VARCHAR(64) NOT NULL COMMENT '设备ID（关联设备表）',
    `title` VARCHAR(255) NOT NULL COMMENT '会议标题',
    `status` ENUM('ACTIVE', 'COMPLETED', 'CANCELLED') DEFAULT 'ACTIVE' COMMENT '会议状态',
    `start_time` DATETIME NOT NULL COMMENT '会议开始时间',
    `end_time` DATETIME NULL COMMENT '会议结束时间',
    `total_duration` INT DEFAULT 0 COMMENT '会议总时长（秒）',
    `total_segments` INT DEFAULT 0 COMMENT '会议片段总数',
    `total_words` INT DEFAULT 0 COMMENT '会议总字数',
    `creator` BIGINT COMMENT '创建者ID',
    `create_date` DATETIME COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者ID',
    `update_date` DATETIME COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_status` (`status`),
    KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议记录表';

-- 会议片段表
DROP TABLE IF EXISTS `ai_meeting_segment`;
CREATE TABLE `ai_meeting_segment` (
    `id` VARCHAR(32) NOT NULL COMMENT '片段唯一标识',
    `meeting_id` VARCHAR(32) NOT NULL COMMENT '会议ID（关联会议表）',
    `segment_index` INT NOT NULL COMMENT '片段序号',
    `content` LONGTEXT NOT NULL COMMENT '片段内容',
    `word_count` INT DEFAULT 0 COMMENT '片段字数',
    `start_time` DATETIME NOT NULL COMMENT '片段开始时间',
    `end_time` DATETIME NULL COMMENT '片段结束时间',
    `duration` INT DEFAULT 0 COMMENT '片段时长（秒）',
    `speaker_info` JSON NULL COMMENT '说话人信息（JSON格式）',
    `creator` BIGINT COMMENT '创建者ID',
    `create_date` DATETIME COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者ID',
    `update_date` DATETIME COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_meeting_id` (`meeting_id`),
    KEY `idx_segment_index` (`meeting_id`, `segment_index`),
    KEY `idx_start_time` (`start_time`),
    FOREIGN KEY (`meeting_id`) REFERENCES `ai_meeting`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议片段表';

-- 会议分析结果表
DROP TABLE IF EXISTS `ai_meeting_analysis`;
CREATE TABLE `ai_meeting_analysis` (
    `id` VARCHAR(32) NOT NULL COMMENT '分析结果唯一标识',
    `meeting_id` VARCHAR(32) NOT NULL COMMENT '会议ID（关联会议表）',
    `analysis_type` ENUM('minutes', 'summary', 'suggestions') NOT NULL COMMENT '分析类型',
    `title` VARCHAR(255) NULL COMMENT '分析结果标题',
    `content` LONGTEXT NOT NULL COMMENT '分析结果内容',
    `word_count` INT DEFAULT 0 COMMENT '分析结果字数',
    `analysis_time` DATETIME NOT NULL COMMENT '分析时间',
    `creator` BIGINT COMMENT '创建者ID',
    `create_date` DATETIME COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者ID',
    `update_date` DATETIME COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_meeting_id` (`meeting_id`),
    KEY `idx_analysis_type` (`analysis_type`),
    KEY `idx_analysis_time` (`analysis_time`),
    FOREIGN KEY (`meeting_id`) REFERENCES `ai_meeting`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议分析结果表';

-- 会议问答记录表
DROP TABLE IF EXISTS `ai_meeting_qa`;
CREATE TABLE `ai_meeting_qa` (
    `id` VARCHAR(32) NOT NULL COMMENT '问答记录唯一标识',
    `meeting_id` VARCHAR(32) NOT NULL COMMENT '会议ID（关联会议表）',
    `question` TEXT NOT NULL COMMENT '用户问题',
    `answer` LONGTEXT NOT NULL COMMENT '智能回答',
    `context_segments` JSON NULL COMMENT '相关片段上下文（JSON格式）',
    `confidence_score` DECIMAL(3,2) DEFAULT 0.00 COMMENT '答案置信度（0-1）',
    `response_time` INT DEFAULT 0 COMMENT '响应时间（毫秒）',
    `creator` BIGINT COMMENT '创建者ID',
    `create_date` DATETIME COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_meeting_id` (`meeting_id`),
    KEY `idx_create_date` (`create_date`),
    FOREIGN KEY (`meeting_id`) REFERENCES `ai_meeting`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议问答记录表';
