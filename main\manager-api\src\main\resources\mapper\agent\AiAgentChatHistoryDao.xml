<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xiaozhi.modules.agent.dao.AiAgentChatHistoryDao">
  <resultMap id="BaseResultMap" type="xiaozhi.modules.agent.entity.AgentChatHistoryEntity">
    <!--@mbg.generated-->
    <!--@Table ai_agent_chat_history-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mac_address" jdbcType="VARCHAR" property="macAddress" />
    <result column="agent_id" jdbcType="VARCHAR" property="agentId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="sort" jdbcType="BIGINT" property="sort" />
    <result column="chat_type" jdbcType="TINYINT" property="chatType" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="audio" jdbcType="LONGVARCHAR" property="audio" />
    <result column="audio_url" jdbcType="VARCHAR" property="audioUrl" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, mac_address, agent_id, session_id, sort, chat_type, content, audio, audio_url, 
    created_at, updated_at
  </sql>

  <delete id="deleteAudioByAgentId">
    DELETE FROM ai_agent_chat_audio 
    WHERE id IN (
      SELECT audio_id 
      FROM ai_agent_chat_history 
      WHERE agent_id = #{agentId}
    )
  </delete>
  
  <update id="deleteAudioIdByAgentId">
    UPDATE ai_agent_chat_history
    SET audio_id = NULL
    WHERE agent_id = #{agentId}
  </update>

  <delete id="deleteHistoryByAgentId">
    DELETE FROM ai_agent_chat_history 
    WHERE agent_id = #{agentId}
  </delete>
</mapper>
