# 会议管理器
import json
import asyncio
from datetime import datetime
from core.utils.logger import get_logger
from config.manage_api_client import (
    ManageApiClient, create_meeting, update_meeting_status,
    add_meeting_segment, update_meeting_segment,
    get_meeting_content, save_meeting_analysis,
    get_recent_meetings, get_meeting_page
)
import re

TAG = __name__
logger = get_logger()

class MeetingApiClient:
    """会议API客户端 - 真实实现"""

    def __init__(self, config):
        self.config = config
        # 使用现有的ManageApiClient单例
        try:
            self.api_client = ManageApiClient(config)
            logger.bind(tag=TAG).info("MeetingApiClient initialized with real API integration")
        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to initialize ManageApiClient: {e}")
            self.api_client = None
    
    async def create_meeting(self, device_id: str, title: str, user_id: int = None) -> dict:
        """创建新会议"""
        try:
            logger.bind(tag=TAG).info(f"Creating meeting: {title} for device: {device_id}")

            # 调用API创建会议
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: create_meeting(device_id, title, user_id)
            )

            if result:
                logger.bind(tag=TAG).info(f"Meeting created successfully: {result.get('id')}")
                return result
            else:
                raise Exception("API returned None")

        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to create meeting: {e}")
            # 返回占位符数据以保证系统继续运行
            return {
                "id": f"temp_{int(datetime.now().timestamp())}",
                "deviceId": device_id,
                "title": title,
                "status": "ACTIVE",
                "startTime": datetime.now().isoformat()
            }
    
    async def update_meeting_status(self, meeting_id: str, status: str, end_time: str = None) -> None:
        """更新会议状态"""
        try:
            logger.bind(tag=TAG).info(f"Updating meeting {meeting_id} status to {status}")

            # 调用API更新会议状态
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: update_meeting_status(meeting_id, status, end_time)
            )

            if result:
                logger.bind(tag=TAG).info(f"Meeting {meeting_id} status updated to {status}")
            else:
                logger.bind(tag=TAG).warning(f"Failed to update meeting {meeting_id} status")

        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to update meeting status: {e}")
    
    async def add_meeting_segment(self, meeting_id: str, segment_data: dict) -> dict:
        """添加会议片段"""
        try:
            logger.bind(tag=TAG).info(f"Adding segment to meeting {meeting_id}")

            # 构建请求数据
            segment_request = {
                "meetingId": meeting_id,
                "segmentIndex": segment_data.get("segmentIndex", 0),
                "startTime": segment_data.get("startTime", datetime.now().isoformat()),
                "content": segment_data.get("content", ""),
                "wordCount": segment_data.get("wordCount", 0)
            }

            # 调用API创建片段
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: add_meeting_segment(meeting_id, segment_request)
            )

            if result:
                logger.bind(tag=TAG).info(f"Meeting segment created successfully: {result.get('id')}")
                return result
            else:
                raise Exception("API returned None")

        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to add meeting segment: {e}")
            # 返回占位符数据
            return {
                "id": f"temp_segment_{int(datetime.now().timestamp())}",
                "meetingId": meeting_id,
                "segmentIndex": segment_data.get("segmentIndex", 0),
                "startTime": segment_data.get("startTime", datetime.now().isoformat()),
                "content": segment_data.get("content", ""),
                "wordCount": segment_data.get("wordCount", 0)
            }
    
    async def update_meeting_segment(self, segment_id: str, content: str, word_count: int) -> None:
        """更新会议片段内容"""
        try:
            logger.bind(tag=TAG).info(f"Updating segment {segment_id}, word count: {word_count}")

            # 调用API更新片段
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: update_meeting_segment(segment_id, content, word_count)
            )

            if result:
                logger.bind(tag=TAG).info(f"Meeting segment {segment_id} updated successfully")
            else:
                logger.bind(tag=TAG).warning(f"Failed to update segment {segment_id}")

        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to update meeting segment: {e}")
    
    async def save_meeting_analysis(self, meeting_id: str, analysis_type: str, content: str) -> dict:
        """保存会议分析结果"""
        try:
            logger.bind(tag=TAG).info(f"Saving {analysis_type} analysis for meeting {meeting_id}")

            # 调用API保存分析结果
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: save_meeting_analysis(meeting_id, analysis_type, content)
            )

            if result:
                logger.bind(tag=TAG).info(f"Meeting analysis saved successfully: {result.get('id')}")
                return result
            else:
                logger.bind(tag=TAG).warning("Failed to save meeting analysis")
                return {"id": f"temp_analysis_{int(datetime.now().timestamp())}"}

        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to save meeting analysis: {e}")
            return {"id": f"temp_analysis_{int(datetime.now().timestamp())}"}
    
    async def get_meeting_content(self, meeting_id: str) -> str:
        """获取完整会议内容"""
        try:
            logger.bind(tag=TAG).info(f"Getting content for meeting {meeting_id}")

            # 调用API获取会议内容
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: get_meeting_content(meeting_id)
            )

            # 提取内容字符串
            if result:
                content = result.get("content", "")
                logger.bind(tag=TAG).info(f"Retrieved meeting content: {len(content)} characters")
                return content
            else:
                logger.bind(tag=TAG).warning("No content returned from API")
                return "会议内容为空或获取失败"

        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to get meeting content: {e}")
            return "会议内容获取失败，请稍后重试。"

    async def get_recent_meetings(self, page: int = 1, limit: int = 10, device_id: str = None, status: str = None) -> dict:
        """获取最近会议列表（分页）"""
        try:
            logger.bind(tag=TAG).info(f"Getting recent meetings: page={page}, limit={limit}")

            # 调用API获取最近会议列表
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: get_recent_meetings(page, limit, device_id, status)
            )

            if result:
                logger.bind(tag=TAG).info(f"Retrieved {len(result.get('list', []))} recent meetings")
                return result
            else:
                logger.bind(tag=TAG).warning("No recent meetings returned from API")
                return {"list": [], "total": 0}

        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to get recent meetings: {e}")
            return {"list": [], "total": 0}

    async def get_meeting_page(self, page: int = 1, limit: int = 10, device_id: str = None,
                              status: str = None, keyword: str = None, start_time: str = None,
                              end_time: str = None) -> dict:
        """分页查询会议列表"""
        try:
            logger.bind(tag=TAG).info(f"Getting meeting page: page={page}, limit={limit}")

            # 调用API分页查询会议列表
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: get_meeting_page(page, limit, device_id, status, keyword, start_time, end_time)
            )

            if result:
                logger.bind(tag=TAG).info(f"Retrieved {len(result.get('list', []))} meetings from page {page}")
                return result
            else:
                logger.bind(tag=TAG).warning("No meetings returned from API")
                return {"list": [], "total": 0}

        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to get meeting page: {e}")
            return {"list": [], "total": 0}

class CommandRecognizer:
    """指令识别器占位符"""
    
    def __init__(self, config):
        self.config = config
        # 默认指令配置占位符
        self.default_commands = {
            "minutes": ["生成会议纪要", "会议纪要", "生成纪要"],
            "summary": ["生成会议总结", "会议总结", "总结会议"],
            "suggestions": ["提出建议方案", "建议方案", "生成建议"],
            "end_meeting": ["结束会议", "停止会议", "会议结束"]
        }
        logger.bind(tag=TAG).info("CommandRecognizer initialized with default commands")
    
    async def recognize_command(self, text: str, device_id: str) -> str:
        """识别语音指令 - 占位符实现"""
        text = text.strip().lower()
        
        for command_type, triggers in self.default_commands.items():
            for trigger in triggers:
                if trigger in text:
                    logger.bind(tag=TAG).info(f"Recognized command: {command_type} from text: {text}")
                    return command_type
        
        return None

class MeetingAnalyzer:
    """会议内容分析器 - 真实实现"""

    def __init__(self, llm_client, api_client):
        self.llm = llm_client
        self.api_client = api_client
        self.analysis_prompts = {
            "minutes": """请基于以下会议内容生成详细的会议纪要：

会议内容：
{content}

请按以下格式生成会议纪要：
1. 会议概述
2. 主要讨论内容
3. 决策事项
4. 行动计划
5. 后续跟进

要求：内容准确、条理清晰、重点突出。""",

            "summary": """请对以下会议内容进行总结：

会议内容：
{content}

请生成简洁明了的会议总结，包括：
- 会议主题和目标
- 关键讨论点
- 主要结论
- 重要决定

要求：语言简练、重点突出。""",

            "suggestions": """请基于以下会议内容提出建议方案：

会议内容：
{content}

请分析会议中提到的问题和挑战，并提出：
1. 问题分析
2. 解决方案建议
3. 实施步骤
4. 风险评估
5. 预期效果

要求：建议具体可行、逻辑清晰。"""
        }
        logger.bind(tag=TAG).info("MeetingAnalyzer initialized with real LLM integration")
        
    async def generate_analysis_streaming(self, conn, meeting_content: str, analysis_type: str, meeting_id: str):
        """流式生成分析结果 - 真实LLM实现"""
        try:
            # 1. 发送分析开始通知
            await conn.websocket.send(json.dumps({
                "type": "tts",
                "status": "sentence_start",
                "text": f"正在生成{self._get_analysis_name(analysis_type)}...",
                "analysis_type": analysis_type,
                "meeting_id": meeting_id
            }))

            # 2. 构建分析提示词
            prompt = self.analysis_prompts.get(analysis_type, self.analysis_prompts["summary"])
            formatted_prompt = prompt.format(content=meeting_content)

            # 3. 使用真实LLM进行流式生成
            session_id = f"meeting_analysis_{meeting_id}_{analysis_type}"
            full_content = ""

            if self.llm and hasattr(self.llm, 'chat_stream'):
                # 使用LLM流式生成
                async for chunk in self.llm.chat_stream(formatted_prompt):
                    if conn.client_abort:
                        break

                    if chunk and chunk.strip():
                        full_content += chunk

                        # 发送流式内容
                        await conn.websocket.send(json.dumps({
                            "type": "llm",
                            "text": chunk,
                            "session_id": session_id,
                            "analysis_type": analysis_type,
                            "meeting_id": meeting_id
                        }))

                        # 短暂延迟以控制发送速度
                        await asyncio.sleep(0.05)
            else:
                # 降级到占位符实现
                logger.bind(tag=TAG).warning("LLM not available, using placeholder analysis")
                analysis_content = self._generate_placeholder_analysis(analysis_type, meeting_content)
                chunks = self._split_into_chunks(analysis_content, 50)

                for chunk in chunks:
                    if conn.client_abort:
                        break

                    full_content += chunk

                    await conn.websocket.send(json.dumps({
                        "type": "llm",
                        "text": chunk,
                        "session_id": session_id,
                        "analysis_type": analysis_type,
                        "meeting_id": meeting_id
                    }))

                    await asyncio.sleep(0.1)

            # 4. 通过API保存分析结果
            await self.api_client.save_meeting_analysis(meeting_id, analysis_type, full_content)
            
            # 5. 发送分析完成通知
            await conn.websocket.send(json.dumps({
                "type": "tts",
                "status": "sentence_end",
                "analysis_type": analysis_type,
                "meeting_id": meeting_id
            }))
            
            return full_content
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"生成分析失败: {e}")
            return ""
    
    def _generate_placeholder_analysis(self, analysis_type: str, meeting_content: str) -> str:
        """生成占位符分析内容"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
        
        if analysis_type == "minutes":
            return f"""## 会议纪要

**时间**：{current_time}
**主要议题**：
1. 项目进展讨论
2. 技术方案评审
3. 下阶段计划制定

**决议事项**：
1. 确定技术架构方案
2. 明确各组件责任分工
3. 制定开发时间表

**后续行动**：
1. 完成详细设计文档
2. 开始编码实现
3. 定期进展汇报

*注：这是占位符生成的会议纪要，实际内容将基于真实会议内容生成。*"""

        elif analysis_type == "summary":
            return f"""## 会议总结

**主要讨论要点**：
• 确认了项目的技术方向和实现路径
• 讨论了各个模块的接口设计
• 明确了团队分工和时间节点

**重要结论**：
• 采用分层架构设计
• 优先实现核心功能
• 重视系统稳定性和扩展性

*注：这是占位符生成的会议总结，实际内容将基于真实会议内容生成。*"""

        elif analysis_type == "suggestions":
            return f"""## 建议方案

**技术建议**：
1. 建议优先实现WebSocket协议扩展
2. 建议采用渐进式开发策略
3. 建议加强错误处理和日志记录

**流程建议**：
1. 建议定期进行代码评审
2. 建议加强测试覆盖率
3. 建议完善文档和注释

**资源建议**：
1. 建议增加开发环境配置
2. 建议优化部署流程
3. 建议建立监控机制

*注：这是占位符生成的建议方案，实际内容将基于真实会议内容生成。*"""

        return "占位符分析内容"
    
    def _split_into_chunks(self, text: str, chunk_size: int) -> list:
        """将文本分割成块"""
        return [text[i:i+chunk_size] for i in range(0, len(text), chunk_size)]
    
    def _get_analysis_name(self, analysis_type: str) -> str:
        """获取分析类型的中文名称"""
        names = {
            "minutes": "会议纪要",
            "summary": "会议总结", 
            "suggestions": "建议方案"
        }
        return names.get(analysis_type, "分析结果")

class MeetingManager:
    """会议生命周期管理器"""
    
    def __init__(self, conn, analyzer, api_client):
        self.conn = conn
        self.analyzer = analyzer
        self.api_client = api_client
        self.current_meeting = None
        self.current_segment = None
        logger.bind(tag=TAG).info("MeetingManager initialized")
        
    async def start_meeting(self, title: str):
        """开始会议"""
        try:
            # 通过API创建会议记录
            meeting_data = await self.api_client.create_meeting(
                device_id=self.conn.device_id,
                title=title
            )
            self.current_meeting = meeting_data
            
            # 创建第一个片段
            segment_data = {
                "segmentIndex": 0,
                "startTime": datetime.now().isoformat(),
                "content": "",
                "wordCount": 0
            }
            segment_response = await self.api_client.add_meeting_segment(
                self.current_meeting["id"], 
                segment_data
            )
            self.current_segment = {
                "id": segment_response["id"],
                "content": "",
                "word_count": 0,
                "start_time": datetime.now()
            }
            
            # 发送会议开始确认
            await self.conn.websocket.send(json.dumps({
                "type": "meeting",
                "action": "status_response", 
                "data": {
                    "meeting_id": self.current_meeting["id"],
                    "status": "active",
                    "duration": 0,
                    "segments": 1
                }
            }))
            
            logger.bind(tag=TAG).info(f"Meeting started: {title}, ID: {self.current_meeting['id']}")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"创建会议失败: {e}")
            
    async def end_meeting(self):
        """结束会议"""
        if self.current_meeting:
            try:
                # 保存当前片段
                if self.current_segment and self.current_segment["content"]:
                    await self._save_current_segment()
                
                # 更新会议状态为已完成
                await self.api_client.update_meeting_status(
                    self.current_meeting["id"], 
                    "COMPLETED",
                    datetime.now().isoformat()
                )
                
                logger.bind(tag=TAG).info(f"Meeting ended: {self.current_meeting['id']}")
                
                self.current_meeting = None
                self.current_segment = None
                
            except Exception as e:
                logger.bind(tag=TAG).error(f"结束会议失败: {e}")
            
    async def add_meeting_content(self, text: str):
        """添加会议内容（累积到当前片段）"""
        if not self.current_meeting or not self.current_segment:
            logger.bind(tag=TAG).warning("No active meeting or segment to add content to")
            return
            
        # 处理可能包含说话人信息的JSON格式文本
        processed_text = self._extract_content_from_text(text)
        
        if processed_text.strip():  # 只添加非空内容
            # 添加时间戳
            timestamp = datetime.now().strftime("%H:%M:%S")
            content_line = f"[{timestamp}] {processed_text}"
            
            self.current_segment["content"] += f"{content_line}\n"
            self.current_segment["word_count"] += len(processed_text)
            
            logger.bind(tag=TAG).debug(f"Added content to meeting {self.current_meeting['id']}: {len(processed_text)} chars")
            
            # 检查是否需要创建新片段（简化的分段策略）
            if await self._should_create_new_segment():
                await self._save_current_segment()
                await self._create_new_segment()
    
    def _extract_content_from_text(self, text: str) -> str:
        """从文本中提取实际内容，处理JSON格式"""
        try:
            # 尝试解析JSON格式（包含说话人信息）
            if text.strip().startswith('{') and text.strip().endswith('}'):
                import json
                data = json.loads(text)
                if isinstance(data, dict):
                    if "content" in data and "speaker" in data:
                        # 包含说话人信息的格式
                        speaker = data["speaker"]
                        content = data["content"]
                        return f"{speaker}: {content}"
                    elif "content" in data:
                        # 只包含内容的格式
                        return data["content"]
        except (json.JSONDecodeError, KeyError):
            # 如果不是JSON格式或解析失败，直接使用原始文本
            pass
        
        return text
    
    async def send_meeting_status(self):
        """发送会议状态响应"""
        if self.current_meeting:
            try:
                duration = int((datetime.now() - self.current_segment["start_time"]).total_seconds()) if self.current_segment else 0
                
                await self.conn.websocket.send(json.dumps({
                    "type": "meeting",
                    "action": "status_response",
                    "data": {
                        "meeting_id": self.current_meeting["id"],
                        "status": "active",
                        "duration": duration,
                        "segments": 1  # 简化，实际可从API获取
                    }
                }))
            except Exception as e:
                logger.bind(tag=TAG).error(f"获取会议状态失败: {e}")
        
    async def handle_analysis_command(self, command_type: str):
        """处理分析指令，触发流式分析"""
        if not self.current_meeting:
            return
            
        try:
            # 先保存当前片段
            if self.current_segment and self.current_segment["content"]:
                await self._save_current_segment()
            
            # 从API获取完整会议内容
            meeting_content = await self.api_client.get_meeting_content(
                self.current_meeting["id"]
            )
            
            # 启动流式分析（在后台执行）
            asyncio.create_task(
                self.analyzer.generate_analysis_streaming(
                    self.conn, 
                    meeting_content, 
                    command_type,
                    self.current_meeting["id"]
                )
            )
            
            logger.bind(tag=TAG).info(f"Triggered analysis: {command_type} for meeting {self.current_meeting['id']}")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"处理分析指令失败: {e}")
    
    async def _should_create_new_segment(self) -> bool:
        """判断是否需要创建新片段 - 简化实现"""
        if not self.current_segment:
            return False
            
        # 检查时长（2小时）和字数（1万字）
        duration = (datetime.now() - self.current_segment["start_time"]).total_seconds()
        return (
            duration > 2 * 3600 or  # 2小时
            self.current_segment["word_count"] > 10000  # 1万字
        )
    
    async def _save_current_segment(self):
        """保存当前片段到API"""
        if self.current_segment and self.current_segment.get("id"):
            try:
                await self.api_client.update_meeting_segment(
                    self.current_segment["id"],
                    self.current_segment["content"],
                    self.current_segment["word_count"]
                )
                logger.bind(tag=TAG).debug(f"Saved segment {self.current_segment['id']}")
            except Exception as e:
                logger.bind(tag=TAG).error(f"保存片段失败: {e}")
    
    async def _create_new_segment(self):
        """创建新的会议片段"""
        try:
            segment_data = {
                "segmentIndex": self.current_segment.get("index", 0) + 1,
                "startTime": datetime.now().isoformat(),
                "content": "",
                "wordCount": 0
            }
            segment_response = await self.api_client.add_meeting_segment(
                self.current_meeting["id"], 
                segment_data
            )
            
            # 重置当前片段
            self.current_segment = {
                "id": segment_response["id"],
                "content": "",
                "word_count": 0,
                "start_time": datetime.now(),
                "index": segment_data["segmentIndex"]
            }
            
            logger.bind(tag=TAG).info(f"Created new segment {self.current_segment['id']}")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"创建新片段失败: {e}") 