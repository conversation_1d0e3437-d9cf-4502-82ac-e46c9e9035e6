package xiaozhi.modules.meeting.service;

import java.util.Date;
import java.util.List;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.meeting.dto.MeetingCreateDTO;
import xiaozhi.modules.meeting.dto.MeetingPageDTO;
import xiaozhi.modules.meeting.dto.MeetingUpdateDTO;
import xiaozhi.modules.meeting.entity.MeetingEntity;
import xiaozhi.modules.meeting.vo.MeetingDetailVO;
import xiaozhi.modules.meeting.vo.MeetingVO;

/**
 * 会议管理服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface MeetingService extends BaseService<MeetingEntity> {

    /**
     * 分页查询会议列表
     * 
     * @param dto 分页查询参数
     * @return 会议列表分页数据
     */
    PageData<MeetingVO> page(MeetingPageDTO dto);

    /**
     * 获取会议详情
     * 
     * @param id 会议ID
     * @return 会议详情
     */
    MeetingDetailVO getDetail(String id);

    /**
     * 创建会议
     * 
     * @param dto 创建会议参数
     * @return 会议信息
     */
    MeetingVO create(MeetingCreateDTO dto);

    /**
     * 更新会议信息
     * 
     * @param dto 更新会议参数
     */
    void update(MeetingUpdateDTO dto);

    /**
     * 删除会议
     * 
     * @param id 会议ID
     */
    void delete(String id);

    /**
     * 批量删除会议
     * 
     * @param ids 会议ID列表
     */
    void deleteBatch(String[] ids);

    /**
     * 结束会议
     * 
     * @param id 会议ID
     */
    void endMeeting(String id);

    /**
     * 取消会议
     * 
     * @param id 会议ID
     */
    void cancelMeeting(String id);

    /**
     * 更新会议统计信息
     * 
     * @param id 会议ID
     * @param totalSegments 总片段数
     * @param totalWords 总字数
     */
    void updateStatistics(String id, Integer totalSegments, Integer totalWords);

    /**
     * 根据用户ID查询会议列表
     * 
     * @param userId 用户ID
     * @return 会议列表
     */
    List<MeetingVO> getByUserId(Long userId);

    /**
     * 根据设备ID查询会议列表
     * 
     * @param deviceId 设备ID
     * @return 会议列表
     */
    List<MeetingVO> getByDeviceId(String deviceId);

    /**
     * 根据状态查询会议列表
     * 
     * @param status 会议状态
     * @return 会议列表
     */
    List<MeetingVO> getByStatus(String status);

    /**
     * 根据用户ID和状态查询会议列表
     * 
     * @param userId 用户ID
     * @param status 会议状态
     * @return 会议列表
     */
    List<MeetingVO> getByUserIdAndStatus(Long userId, String status);

    /**
     * 根据时间范围查询会议列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会议列表
     */
    List<MeetingVO> getByTimeRange(Long userId, Date startTime, Date endTime);

    /**
     * 根据关键词搜索会议
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 会议列表
     */
    List<MeetingVO> searchByKeyword(Long userId, String keyword);

    /**
     * 统计用户会议数量
     * 
     * @param userId 用户ID
     * @return 会议数量
     */
    Integer countByUserId(Long userId);

    /**
     * 统计用户指定状态的会议数量
     * 
     * @param userId 用户ID
     * @param status 会议状态
     * @return 会议数量
     */
    Integer countByUserIdAndStatus(Long userId, String status);

    /**
     * 获取会议完整内容
     * 
     * @param id 会议ID
     * @return 完整内容
     */
    String getFullContent(String id);
}
