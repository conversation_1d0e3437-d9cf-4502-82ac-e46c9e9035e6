# 会议监控系统 - WebSocket通信流程

```mermaid
sequenceDiagram
    participant ESP32 as ESP32客户端
    participant WS as WebSocket服务器
    participant ASR as ASR识别器
    participant M<PERSON><PERSON><PERSON> as MeetingManager
    participant API as manager-api
    participant LLM as LLM分析器

    Note over ESP32, LLM: 1. 开始会议监听流程
    ESP32->>WS: {"type": "listen", "mode": "meeting", "state": "start", "data": {"title": "项目讨论会议"}}
    WS->>MManager: 处理会议开始请求
    MManager->>API: 创建会议记录
    API-->>MManager: 返回会议ID和详情
    MManager->>MManager: 创建第一个会议片段
    WS->>ESP32: {"type": "meeting", "action": "status_response", "data": {"meeting_id": 12345, "status": "active", "duration": 0, "segments": 1}}

    Note over ESP32, LLM: 2. 会议音频数据处理流程
    ESP32->>WS: 音频数据包 (Opus格式)
    WS->>ASR: 接收并处理音频数据
    ASR->>ASR: VAD检测语音活动
    ASR->>ASR: 语音识别转文本
    ASR->>WS: 识别完成，返回文本
    WS->>ESP32: {"type": "stt", "text": "这是识别出的会议发言", "session_id": "xxx"}
    WS->>MManager: 添加识别文本到会议片段
    MManager->>MManager: 累积会议内容（不发送给LLM）
    MManager->>API: 定期保存片段内容

    Note over ESP32, LLM: 3. 会议内容detect消息处理
    ESP32->>WS: {"type": "listen", "mode": "meeting", "state": "detect", "text": "这是会议中的发言内容"}
    WS->>MManager: 处理文本detect消息
    MManager->>MManager: 检查是否为指令关键词
    alt 非指令内容
        MManager->>MManager: 添加普通文本到会议片段
        MManager->>API: 保存会议内容
        Note over MManager: 不触发LLM处理
    else 检测到指令
        MManager->>MManager: 识别指令类型 (minutes/summary/suggestions)
        Note over MManager, LLM: 跳转到指令处理流程
    end

    Note over ESP32, LLM: 4. 会议状态查询流程
    ESP32->>WS: {"type": "meeting", "action": "status"}
    WS->>MManager: 查询会议状态
    MManager-->>WS: 返回当前状态
    WS->>ESP32: {"type": "meeting", "action": "status_response", "data": {"meeting_id": 12345, "status": "active", "duration": 1800, "segments": 3}}

    Note over ESP32, LLM: 5. 会议指令处理流程（音频或detect触发）
    alt 音频指令
        ESP32->>WS: 音频数据包 (包含指令)
        WS->>ASR: 音频识别
        ASR->>WS: 识别结果："生成会议纪要"
        WS->>ESP32: {"type": "stt", "text": "生成会议纪要", "session_id": "xxx"}
        WS->>MManager: 检测到会议指令
    else detect指令
        ESP32->>WS: {"type": "listen", "mode": "meeting", "state": "detect", "text": "生成会议纪要"}
        WS->>MManager: 处理detect指令
    end
    
    MManager->>MManager: 识别为"minutes"指令
    MManager->>MManager: 保存当前片段
    MManager->>API: 获取完整会议内容
    API-->>MManager: 返回累积的会议记录
    
    Note over MManager, LLM: 流式分析开始（只有指令触发时）
    MManager->>LLM: 启动会议纪要生成任务
    LLM->>WS: {"type": "tts", "status": "sentence_start", "text": "正在生成会议纪要...", "analysis_type": "minutes", "meeting_id": 12345}
    WS->>ESP32: 分析开始通知
    
    Note over LLM, ESP32: 流式内容推送
    loop 流式响应
        LLM->>WS: {"type": "llm", "text": "## 会议纪要<br/>**时间**：...", "session_id": "meeting_analysis_12345", "analysis_type": "minutes", "meeting_id": 12345}
        WS->>ESP32: 推送分析内容
    end
    
    LLM->>API: 保存完整分析结果
    LLM->>WS: {"type": "tts", "status": "sentence_end", "analysis_type": "minutes", "meeting_id": 12345}
    WS->>ESP32: 分析完成通知

    Note over ESP32, LLM: 6. 继续会议内容记录
    alt 音频方式
        ESP32->>WS: 继续发送音频数据
        WS->>ASR: 持续识别
        ASR->>WS: 返回识别文本
        WS->>ESP32: {"type": "stt", "text": "会议继续进行的内容", "session_id": "xxx"}
    else detect方式
        ESP32->>WS: {"type": "listen", "mode": "meeting", "state": "detect", "text": "会议继续进行的内容"}
    end
    WS->>MManager: 继续累积会议内容（不触发LLM）
    MManager->>API: 持续保存会议记录

    Note over ESP32, LLM: 7. 结束会议流程
    ESP32->>WS: {"type": "listen", "mode": "meeting", "state": "stop"}
    WS->>MManager: 处理会议结束
    MManager->>MManager: 保存当前片段
    MManager->>API: 更新会议状态为已完成
    API-->>MManager: 确认更新
    MManager->>MManager: 清理会议状态

    Note over ESP32, LLM: 错误处理流程
    alt 音频识别失败
        ASR--xWS: 识别错误
        WS->>ESP32: {"type": "stt", "text": "", "session_id": "xxx"}
        WS->>MManager: 记录空内容（不影响会议流程）
    end
    
    alt detect消息处理失败
        ESP32->>WS: detect消息
        WS--xMManager: 处理失败
        WS->>ESP32: 错误响应或静默处理
    end
    
    alt API调用失败
        MManager->>API: API请求
        API--xMManager: 请求失败
        MManager->>MManager: 本地缓存会议内容，记录错误日志
    end

```