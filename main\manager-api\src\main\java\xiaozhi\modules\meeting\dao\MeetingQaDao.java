package xiaozhi.modules.meeting.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.meeting.entity.MeetingQaEntity;

/**
 * 会议问答记录DAO接口
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Mapper
public interface MeetingQaDao extends BaseMapper<MeetingQaEntity> {

    /**
     * 根据会议ID查询所有问答记录
     * 
     * @param meetingId 会议ID
     * @return 问答记录列表
     */
    @Select("SELECT * FROM ai_meeting_qa WHERE meeting_id = #{meetingId} ORDER BY create_date DESC")
    List<MeetingQaEntity> selectByMeetingId(@Param("meetingId") String meetingId);

    /**
     * 根据用户ID查询问答记录（通过关联会议表）
     * 
     * @param userId 用户ID
     * @return 问答记录列表
     */
    @Select("SELECT q.* FROM ai_meeting_qa q " +
            "INNER JOIN ai_meeting m ON q.meeting_id = m.id " +
            "WHERE m.user_id = #{userId} " +
            "ORDER BY q.create_date DESC")
    List<MeetingQaEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据问题关键词搜索问答记录
     * 
     * @param meetingId 会议ID
     * @param keyword 关键词
     * @return 问答记录列表
     */
    @Select("SELECT * FROM ai_meeting_qa WHERE meeting_id = #{meetingId} AND question LIKE CONCAT('%', #{keyword}, '%') ORDER BY create_date DESC")
    List<MeetingQaEntity> searchByQuestion(@Param("meetingId") String meetingId, @Param("keyword") String keyword);

    /**
     * 根据答案关键词搜索问答记录
     * 
     * @param meetingId 会议ID
     * @param keyword 关键词
     * @return 问答记录列表
     */
    @Select("SELECT * FROM ai_meeting_qa WHERE meeting_id = #{meetingId} AND answer LIKE CONCAT('%', #{keyword}, '%') ORDER BY create_date DESC")
    List<MeetingQaEntity> searchByAnswer(@Param("meetingId") String meetingId, @Param("keyword") String keyword);

    /**
     * 根据关键词搜索问答记录（问题和答案）
     * 
     * @param meetingId 会议ID
     * @param keyword 关键词
     * @return 问答记录列表
     */
    @Select("SELECT * FROM ai_meeting_qa WHERE meeting_id = #{meetingId} AND (question LIKE CONCAT('%', #{keyword}, '%') OR answer LIKE CONCAT('%', #{keyword}, '%')) ORDER BY create_date DESC")
    List<MeetingQaEntity> searchByKeyword(@Param("meetingId") String meetingId, @Param("keyword") String keyword);

    /**
     * 根据用户ID和关键词搜索问答记录
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 问答记录列表
     */
    @Select("SELECT q.* FROM ai_meeting_qa q " +
            "INNER JOIN ai_meeting m ON q.meeting_id = m.id " +
            "WHERE m.user_id = #{userId} AND (q.question LIKE CONCAT('%', #{keyword}, '%') OR q.answer LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY q.create_date DESC")
    List<MeetingQaEntity> searchByUserIdAndKeyword(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 统计会议的问答记录数量
     * 
     * @param meetingId 会议ID
     * @return 问答记录数量
     */
    @Select("SELECT COUNT(*) FROM ai_meeting_qa WHERE meeting_id = #{meetingId}")
    Integer countByMeetingId(@Param("meetingId") String meetingId);

    /**
     * 统计用户的问答记录数量
     * 
     * @param userId 用户ID
     * @return 问答记录数量
     */
    @Select("SELECT COUNT(*) FROM ai_meeting_qa q " +
            "INNER JOIN ai_meeting m ON q.meeting_id = m.id " +
            "WHERE m.user_id = #{userId}")
    Integer countByUserId(@Param("userId") Long userId);

    /**
     * 获取平均响应时间
     * 
     * @param meetingId 会议ID
     * @return 平均响应时间（毫秒）
     */
    @Select("SELECT AVG(response_time) FROM ai_meeting_qa WHERE meeting_id = #{meetingId} AND response_time > 0")
    Double getAverageResponseTime(@Param("meetingId") String meetingId);

    /**
     * 获取平均置信度
     * 
     * @param meetingId 会议ID
     * @return 平均置信度
     */
    @Select("SELECT AVG(confidence_score) FROM ai_meeting_qa WHERE meeting_id = #{meetingId} AND confidence_score > 0")
    Double getAverageConfidenceScore(@Param("meetingId") String meetingId);

    /**
     * 删除会议的所有问答记录
     * 
     * @param meetingId 会议ID
     * @return 删除行数
     */
    @Select("DELETE FROM ai_meeting_qa WHERE meeting_id = #{meetingId}")
    int deleteByMeetingId(@Param("meetingId") String meetingId);
}
