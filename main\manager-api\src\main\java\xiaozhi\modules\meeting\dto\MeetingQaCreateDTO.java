package xiaozhi.modules.meeting.dto;

import java.io.Serializable;
import java.math.BigDecimal;


import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 创建会议问答记录DTO
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Schema(description = "创建会议问答记录DTO")
public class MeetingQaCreateDTO implements Serializable {

    @Schema(description = "会议ID", required = true)
    @NotBlank(message = "会议ID不能为空")
    private String meetingId;

    @Schema(description = "用户问题", required = true)
    @NotBlank(message = "用户问题不能为空")
    private String question;

    @Schema(description = "智能回答", required = true)
    @NotBlank(message = "智能回答不能为空")
    private String answer;

    @Schema(description = "相关片段上下文（JSON格式）")
    private JSONObject contextSegments;

    @Schema(description = "答案置信度（0-1）")
    private BigDecimal confidenceScore;

    @Schema(description = "响应时间（毫秒）")
    private Integer responseTime;
}
